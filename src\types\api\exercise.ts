/**
 * Exercise-related API Types
 *
 * These TypeScript interfaces map to exercise models from the DrMuscle production API.
 */

import type { MultiUnityWeight, WarmUps, WorkoutLogSerieModel } from './workout'

/**
 * Represents an exercise within a workout
 * Note: Backend uses "ExerciceModel" (French spelling)
 * @example
 * const exercise: ExerciseModel = {
 *   Id: 1,
 *   Label: "Bench Press",
 *   IsBodyweight: false,
 *   // ... other fields
 * }
 */
export interface ExerciseModel {
  Id: number
  Label: string
  IsSystemExercise: boolean
  IsSwapTarget: boolean
  IsFinished: boolean
  BodyPartId?: number
  IsUnilateral: boolean
  IsTimeBased: boolean
  EquipmentId?: number
  IsEasy: boolean
  IsMedium: boolean
  IsBodyweight: boolean
  VideoUrl: string
  IsNextExercise: boolean
  IsPlate: boolean
  IsWeighted: boolean
  IsPyramid: boolean
  RepsMaxValue?: number
  RepsMinValue?: number
  Timer?: number
  IsNormalSets: boolean
  WorkoutGroupId?: number
  IsBodypartPriority: boolean
  IsFlexibility: boolean
  IsOneHanded: boolean
  LocalVideo: string
  IsAssisted: boolean
  SetStyle?: string
}

/**
 * Exercise model with AI recommendations
 */
export interface ExerciseModelWithReco extends ExerciseModel {
  RecoModel: RecommendationModel
  IsSelected: boolean
}

/**
 * Contains AI-generated recommendations for sets, reps, weight, and progression
 * @example
 * const recommendation: RecommendationModel = {
 *   Series: 3,
 *   Reps: 10,
 *   Weight: { Lb: 135, Kg: 61.23 },
 *   // ... other fields
 * }
 */
export interface RecommendationModel {
  ExerciseId: number
  Series: number
  Reps: number
  Weight: MultiUnityWeight
  OneRMProgress?: number
  RecommendationInKg?: number
  RecommendationInKG?: MultiUnityWeight // Alternative casing from API
  OneRMPercentage?: number
  WarmUpReps1?: number
  WarmUpReps2?: number
  WarmUpWeightSet1?: MultiUnityWeight
  WarmUpWeightSet2?: MultiUnityWeight
  WarmUpsList?: WarmUps[]
  WarmupsCount: number
  RpRest: number
  NbPauses: number
  NbRepsPauses: number
  IsEasy?: boolean
  IsMedium?: boolean
  IsBodyweight?: boolean
  Increments?: MultiUnityWeight
  Max?: MultiUnityWeight
  Min?: MultiUnityWeight
  IsNormalSets?: boolean
  IsDeload?: boolean
  IsBackOffSet?: boolean
  IsDefaultUnilateral?: boolean
  BackOffSetWeight?: MultiUnityWeight
  IsMaxChallenge?: boolean
  IsLightSession?: boolean
  LastLogDate?: string // API returns string, not Date
  FirstWorkSetReps?: number
  FirstWorkSetWeight?: MultiUnityWeight
  FirstWorkSet1RM?: MultiUnityWeight
  IsPyramid?: boolean
  IsReversePyramid?: boolean
  HistorySet?: WorkoutLogSerieModel[]
  ReferenceSetHistory?: WorkoutLogSerieModel | null
  MinReps?: number
  MaxReps?: number
  isPlateAvailable?: boolean
  isDumbbellAvailable?: boolean
  isPulleyAvailable?: boolean
  isBandsAvailable?: boolean
  Speed?: number
  days?: number
  RIR?: number
  IsManual?: boolean
  Id?: number
  ReferenseReps?: number // Note: Typo in backend
  ReferenseWeight?: MultiUnityWeight // Note: Typo in backend
  IsDropSet?: boolean
  // Additional fields from actual API responses
  SetNo?: string
  Trainer?: string
  WeightRecommandation?: number // Note: Typo in backend
  Challenge?: string
  BodyWeight?: number
  IsTimeBased?: boolean
  IsFinished?: boolean
  TrainingMax?: number
  IsFromProgramLog?: boolean
  IsPlate?: boolean
  TotalVolume?: MultiUnityWeight
  TimeCommited?: string
  Timer?: number
  ExerciseName?: string
  VideUrl?: string // Note: Typo in backend
  MaxWeight?: number
  OneRMText?: string
  TargetIntensityPercentage?: number
  TargetRpe?: number
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Plates?: any[] // Plate configuration
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  PlatesInKg?: any[] // Plate configuration in kg
  EquipmentId?: number
  IsSystemExercise?: boolean
  FailureCount?: number
  LastTimeDialogShown?: string
  EquivalentPlates?: string
  SetStyle?: string
  IsNewStarted?: boolean // Indicates if exercise needs initialization
}
