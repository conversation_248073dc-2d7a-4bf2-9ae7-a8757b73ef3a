import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import { fileURLToPath } from 'url'
import os from 'os'

// Optimal worker configuration based on CPU count
const cpuCount = os.cpus().length
// For low CPU systems (2-4 cores), use fewer workers to prevent overhead
// For high CPU systems (8+ cores), use 50% of cores
const optimalWorkers = cpuCount <= 4 
  ? Math.max(1, cpuCount - 1) // Leave 1 CPU free for system
  : Math.max(1, Math.floor(cpuCount * 0.5)) // Use 50% of CPUs

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'happy-dom',
    globals: true,
    setupFiles: './tests/setup.ts',
    css: false,

    // Exclude E2E tests from Vitest (they should only run with <PERSON><PERSON>)
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      '**/cypress/**',
      '**/.{idea,git,cache,output,temp}/**',
      '**/e2e/**',
      'tests/e2e/**',
    ],
    
    // Parallel execution settings
    pool: 'threads', // Use worker threads for better performance
    poolOptions: {
      threads: {
        singleThread: false,
        isolate: true,
        maxThreads: optimalWorkers,
        minThreads: Math.max(1, Math.floor(optimalWorkers / 2)),
      }
    },
    
    // Test isolation for parallel safety
    isolate: true,
    
    // Optimize reporter based on environment and CPU count
    reporters: process.env.CI 
      ? ['verbose'] 
      : cpuCount <= 2 
        ? ['dot'] // Minimal output for low CPU systems
        : [['default', { summary: false }]],
    
    // Disable coverage during regular test runs for speed
    coverage: {
      enabled: false,
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'text-summary'],
      exclude: [
        'node_modules/',
        'tests/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/mockData/**',
        'src/types/**',
      ],
      thresholds: {
        lines: 80,
        functions: 80,
        branches: 80,
        statements: 80,
      },
    },
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
})