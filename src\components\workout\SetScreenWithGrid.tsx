'use client'

import React, { useEffect, useState } from 'react'
import { useNavigation } from '@/contexts/NavigationContext'
import { useSetScreenLogic } from '@/hooks/useSetScreenLogic'
import { ExerciseSetsGrid } from './ExerciseSetsGrid'
import { ExerciseCompleteView } from './ExerciseCompleteView'
import { WorkoutCompleteView } from './WorkoutCompleteView'
import { SetScreenLoadingState } from './SetScreenLoadingState'
import { SetScreenErrorState } from './SetScreenErrorState'
import { RIRPicker } from './RIRPicker'
import { debugLog } from '@/utils/debugLog'
import type { WorkoutLogSerieModel } from '@/types'

interface SetScreenWithGridProps {
  exerciseId?: number
}

export function SetScreenWithGrid({ exerciseId }: SetScreenWithGridProps) {
  const { setTitle } = useNavigation()
  const {
    // State
    currentExercise,
    exercises,
    currentExerciseIndex,
    currentSetIndex,
    isSaving,
    saveError,
    showRIRPicker,
    showComplete,
    showExerciseComplete,
    isTransitioning,
    recommendation,
    isLoading,
    error,
    isLastExercise,
    completedSets,

    // Actions
    setSetData,
    handleSaveSet,
    handleRIRSelect,
    handleRIRCancel,
    refetchRecommendation,
  } = useSetScreenLogic(exerciseId)

  // Track which set is being edited
  const [editingSetIndex, setEditingSetIndex] = useState<number | null>(null)

  // Update navigation title with exercise name
  useEffect(() => {
    if (currentExercise) {
      setTitle(currentExercise.Label)
    }
  }, [currentExercise, setTitle])

  // Generate all sets based on recommendation and completed sets
  const generateAllSets = (): WorkoutLogSerieModel[] => {
    if (!recommendation) return []

    const sets: WorkoutLogSerieModel[] = []
    const warmupCount = recommendation.WarmupsCount || 0
    const workSetCount = recommendation.Series || 0

    // Add warmup sets
    for (let i = 0; i < warmupCount; i++) {
      const isCompleted = completedSets.some(
        (s) => s.IsWarmups && parseInt(s.SetNo || '0') === i + 1
      )
      const completedSet = completedSets.find(
        (s) => s.IsWarmups && parseInt(s.SetNo || '0') === i + 1
      )

      sets.push({
        Id: completedSet?.Id || -(i + 1), // Negative ID for uncompleted sets
        SetNo: `${i + 1}`,
        Reps: completedSet?.Reps || recommendation.WarmUpReps1 || 5,
        Weight: completedSet?.Weight || {
          Lb: recommendation.WarmUpWeightSet1?.Lb || 0,
          Kg: recommendation.WarmUpWeightSet1?.Kg || 0,
        },
        IsFinished: isCompleted,
        IsNext: !isCompleted && currentSetIndex === i,
        IsWarmups: true,
      })
    }

    // Add work sets
    for (let i = 0; i < workSetCount; i++) {
      const setIndex = warmupCount + i
      const isCompleted = completedSets.some(
        (s) => !s.IsWarmups && parseInt(s.SetNo || '0') === setIndex + 1
      )
      const completedSet = completedSets.find(
        (s) => !s.IsWarmups && parseInt(s.SetNo || '0') === setIndex + 1
      )

      sets.push({
        Id: completedSet?.Id || -(setIndex + 1),
        SetNo: `${setIndex + 1}`,
        Reps: completedSet?.Reps || recommendation.Reps || 10,
        Weight: completedSet?.Weight ||
          recommendation.Weight || { Lb: 0, Kg: 0 },
        IsFinished: isCompleted,
        IsNext: !isCompleted && currentSetIndex === setIndex,
        IsWarmups: false,
      })
    }

    return sets
  }

  // Handle set update from grid
  const handleSetUpdate = (
    setId: number,
    updates: { reps?: number; weight?: number }
  ) => {
    debugLog('[SetScreenWithGrid] Updating set:', { setId, updates })

    // Find the set index
    const allSets = generateAllSets()
    const setIndex = allSets.findIndex(
      (s) => s.Id === setId || Math.abs(s.Id || 0) === setId
    )

    if (setIndex !== -1) {
      // Update the current set data
      setSetData((prev) => ({
        ...prev,
        reps: updates.reps ?? prev.reps,
        weight: updates.weight ?? prev.weight,
      }))

      // If this is the current set, it will be saved with the save button
      // If it's a different set, we might need to handle it differently
      setEditingSetIndex(setIndex)
    }
  }

  // Handle finish exercise
  const handleFinishExercise = () => {
    debugLog('[SetScreenWithGrid] Finishing exercise')
    // Navigate to next exercise or complete workout
    if (isLastExercise) {
      // This would trigger workout complete
      handleSaveSet()
    } else {
      // Navigate to next exercise
      const nextExerciseIndex = (currentExerciseIndex ?? 0) + 1
      const nextExercise = exercises?.[nextExerciseIndex]
      if (nextExercise) {
        // Navigation would be handled by the parent/router
        debugLog(
          '[SetScreenWithGrid] Would navigate to next exercise:',
          nextExercise.Label
        )
      }
    }
  }

  // Handle add set
  const handleAddSet = () => {
    debugLog('[SetScreenWithGrid] Adding new set')
    // This would need to be implemented in the workout logic
    // For now, just log it
  }

  // Loading state
  if (isLoading && !recommendation) {
    return <SetScreenLoadingState />
  }

  // Error state
  if (error && !recommendation) {
    return <SetScreenErrorState onRetry={refetchRecommendation} />
  }

  // Exercise complete state
  if (showExerciseComplete) {
    return (
      <ExerciseCompleteView
        exerciseLabel={currentExercise?.Label}
        isLastExercise={isLastExercise}
        nextExerciseLabel={
          exercises && typeof currentExerciseIndex === 'number'
            ? exercises[currentExerciseIndex + 1]?.Label
            : undefined
        }
      />
    )
  }

  // Workout complete state
  if (showComplete) {
    return <WorkoutCompleteView />
  }

  const allSets = generateAllSets()

  return (
    <div className="min-h-[100dvh] bg-bg-primary flex flex-col">
      {/* Scrollable content area */}
      <div className="flex-1 overflow-y-auto pb-24">
        {/* Error Display */}
        {error && (
          <div className="bg-error/10 border-b border-error/20 px-4 py-3">
            <p className="text-error text-sm">{error.toString()}</p>
          </div>
        )}

        {/* Save error */}
        {saveError && (
          <div className="bg-error/10 border-b border-error/20 px-4 py-3">
            <p className="text-error text-sm">{saveError.toString()}</p>
            <button
              onClick={handleSaveSet}
              className="text-error underline text-sm mt-1"
            >
              Retry
            </button>
          </div>
        )}

        {/* Exercise Sets Grid */}
        {currentExercise && (
          <ExerciseSetsGrid
            exercise={currentExercise}
            sets={allSets}
            onSetUpdate={handleSetUpdate}
            onFinishExercise={handleFinishExercise}
            onAddSet={handleAddSet}
            unit="lbs" // This could come from user preferences
          />
        )}
      </div>

      {/* Floating Save Button (for current set) */}
      {editingSetIndex !== null && editingSetIndex === currentSetIndex && (
        <div
          data-testid="floating-save-button"
          className="fixed bottom-6 left-0 right-0 z-50 px-4"
        >
          <div className="max-w-lg mx-auto w-full">
            <button
              onClick={handleSaveSet}
              disabled={isSaving || isTransitioning}
              type="submit"
              className={`w-full py-4 min-h-[56px] rounded-full font-semibold text-lg shadow-theme-xl hover:shadow-theme-2xl transition-all ${
                isSaving || isTransitioning
                  ? 'bg-bg-tertiary text-text-tertiary cursor-not-allowed'
                  : 'bg-brand-primary text-text-inverse hover:bg-brand-primary/90 active:scale-[0.98]'
              }`}
            >
              {(() => {
                if (isSaving) return 'Saving...'
                if (isTransitioning) return 'Loading next set...'
                return 'Save Set'
              })()}
            </button>
          </div>
        </div>
      )}

      {/* RIR Picker */}
      <RIRPicker
        isOpen={showRIRPicker}
        onSelect={handleRIRSelect}
        onCancel={handleRIRCancel}
      />
    </div>
  )
}
