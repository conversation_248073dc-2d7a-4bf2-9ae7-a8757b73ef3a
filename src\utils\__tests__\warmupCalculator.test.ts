import { describe, it, expect, vi } from 'vitest'
import { computeWarmups, WarmupCalculator } from '../warmupCalculator'
import type { RecommendationModel, ExerciseModel } from '@/types'
import type { WarmupCalculationConfig } from '../warmupCalculator'

// Mock getUserSettings
vi.mock('@/stores/authStore', () => ({
  useAuthStore: {
    getState: () => ({
      userSettings: {
        barbellWeight: 45,
        unit: 'lbs',
      },
    }),
  },
}))

describe('computeWarmups', () => {
  const createMockRecommendation = (
    overrides?: Partial<RecommendationModel>
  ): RecommendationModel => ({
    Series: 3,
    Reps: 10,
    Weight: { Lb: 100, Kg: 45.36 },
    WarmupsCount: 2,
    Increments: { Lb: 5, Kg: 2.5 },
    isPlateAvailable: false,
    isDumbbellAvailable: true,
    isPulleyAvailable: false,
    isBandsAvailable: false,
    OneRMProgress: 0,
    RecommendationInKg: 45.36,
    OneRMPercentage: 75,
    WarmUpReps1: 0,
    WarmUpReps2: 0,
    WarmUpWeightSet1: { Lb: 0, Kg: 0 },
    WarmUpWeightSet2: { Lb: 0, Kg: 0 },
    WarmUpsList: [],
    RpRest: 180,
    NbPauses: 0,
    NbRepsPauses: 0,
    IsEasy: false,
    IsMedium: true,
    IsBodyweight: false,
    Min: { Lb: 0, Kg: 0 },
    Max: { Lb: 200, Kg: 90.72 },
    IsNormalSets: true,
    IsDeload: false,
    IsBackOffSet: false,
    BackOffSetWeight: { Lb: 0, Kg: 0 },
    IsMaxChallenge: false,
    IsLightSession: false,
    FirstWorkSetReps: 10,
    FirstWorkSetWeight: { Lb: 100, Kg: 45.36 },
    FirstWorkSet1RM: { Lb: 133, Kg: 60.33 },
    IsPyramid: false,
    IsReversePyramid: false,
    HistorySet: [],
    ReferenceSetHistory: {
      Id: 1,
      Reps: 10,
      Weight: { Lb: 95, Kg: 43.09 },
      IsWarmups: false,
      IsNext: false,
      IsFinished: true,
    },
    MinReps: 8,
    MaxReps: 12,
    Speed: 1,
    IsManual: false,
    ReferenseReps: 10,
    ReferenseWeight: { Lb: 95, Kg: 43.09 },
    IsDropSet: false,
    ...overrides,
  })

  const createMockExercise = (
    overrides?: Partial<ExerciseModel>
  ): ExerciseModel => ({
    Id: 1,
    Label: 'Bench Press',
    IsSystemExercise: true,
    IsSwapTarget: false,
    IsFinished: false,
    IsUnilateral: false,
    IsTimeBased: false,
    IsEasy: false,
    IsMedium: true,
    IsBodyweight: false,
    VideoUrl: '',
    IsNextExercise: false,
    IsPlate: false,
    IsWeighted: true,
    IsPyramid: false,
    IsNormalSets: true,
    IsBodypartPriority: false,
    IsFlexibility: false,
    IsOneHanded: false,
    LocalVideo: '',
    IsAssisted: false,
    ...overrides,
  })

  describe('when WarmupsCount is 0', () => {
    it('should return empty array', () => {
      const recommendation = createMockRecommendation({ WarmupsCount: 0 })
      const exercise = createMockExercise()

      const result = computeWarmups(recommendation, exercise)

      expect(result).toEqual([])
    })
  })

  describe('for weighted exercises', () => {
    it('should calculate 2 warmup sets correctly', () => {
      const recommendation = createMockRecommendation({
        WarmupsCount: 2,
        Weight: { Lb: 100, Kg: 45.36 },
        Reps: 10,
      })
      const exercise = createMockExercise({ IsBodyweight: false })

      const result = computeWarmups(recommendation, exercise)

      expect(result).toHaveLength(2)

      // First warmup: bodyweight only (0 weight)
      expect(result[0].warmUpWeightSet.Lb).toBe(0)
      expect(result[0].warmUpWeightSet.Kg).toBe(0)
      expect(result[0].warmUpReps).toBeGreaterThan(0)

      // Second warmup: ~85% of working weight
      expect(result[1].warmUpWeightSet.Lb).toBeCloseTo(85, 0)
      expect(result[1].warmUpReps).toBeLessThan(result[0].warmUpReps)
    })

    it('should calculate 3 warmup sets with proper progression', () => {
      const recommendation = createMockRecommendation({
        WarmupsCount: 3,
        Weight: { Lb: 200, Kg: 90.72 },
        Reps: 8,
      })
      const exercise = createMockExercise({ IsBodyweight: false })

      const result = computeWarmups(recommendation, exercise)

      expect(result).toHaveLength(3)

      // First warmup: bodyweight only
      expect(result[0].warmUpWeightSet.Lb).toBe(0)

      // Progressive weight increase
      expect(result[1].warmUpWeightSet.Lb).toBeGreaterThan(
        result[0].warmUpWeightSet.Lb
      )
      expect(result[2].warmUpWeightSet.Lb).toBeGreaterThan(
        result[1].warmUpWeightSet.Lb
      )

      // Progressive reps decrease
      expect(result[0].warmUpReps).toBeGreaterThan(result[1].warmUpReps)
      expect(result[1].warmUpReps).toBeGreaterThan(result[2].warmUpReps)
    })

    it('should ensure minimum 3 reps when plates not available', () => {
      const recommendation = createMockRecommendation({
        WarmupsCount: 2,
        Weight: { Lb: 300, Kg: 136.08 },
        Reps: 5,
        isPlateAvailable: false,
      })
      const exercise = createMockExercise({ IsBodyweight: false })

      const result = computeWarmups(recommendation, exercise)

      result.forEach((warmup, index) => {
        if (index > 0) {
          // Skip first bodyweight warmup
          expect(warmup.warmUpReps).toBeGreaterThanOrEqual(3)
        }
      })
    })
  })

  describe('for bodyweight exercises', () => {
    it('should calculate warmup sets with same weight but reduced reps', () => {
      const recommendation = createMockRecommendation({
        WarmupsCount: 2,
        Weight: { Lb: 0, Kg: 0 },
        Reps: 15,
        IsBodyweight: true,
      })
      const exercise = createMockExercise({ IsBodyweight: true })

      const result = computeWarmups(recommendation, exercise)

      expect(result).toHaveLength(2)

      // All warmups should have same weight (bodyweight)
      result.forEach((warmup) => {
        expect(warmup.warmUpWeightSet.Lb).toBe(0)
        expect(warmup.warmUpWeightSet.Kg).toBe(0)
      })

      // First warmup: ~40% of target reps
      expect(result[0].warmUpReps).toBeCloseTo(6, 0)

      // Second warmup: ~60% of target reps
      expect(result[1].warmUpReps).toBeCloseTo(9, 0)
    })

    it('should handle single warmup set for bodyweight', () => {
      const recommendation = createMockRecommendation({
        WarmupsCount: 1,
        Weight: { Lb: 0, Kg: 0 },
        Reps: 20,
        IsBodyweight: true,
      })
      const exercise = createMockExercise({ IsBodyweight: true })

      const result = computeWarmups(recommendation, exercise)

      expect(result).toHaveLength(1)
      expect(result[0].warmUpReps).toBe(8) // 40% of 20
    })
  })

  describe('with plate calculations', () => {
    it('should apply plate weight calculations when plates available', () => {
      const recommendation = createMockRecommendation({
        WarmupsCount: 2,
        Weight: { Lb: 135, Kg: 61.23 },
        Reps: 10,
        isPlateAvailable: true,
      })
      const exercise = createMockExercise({
        IsBodyweight: false,
        IsPlate: true,
      })

      const result = computeWarmups(recommendation, exercise)

      // Weights should be rounded to plate increments
      // Assuming barbell weight of 45 lbs from mock
      // Second warmup should be ~85% of 135 = 114.75, rounded to 115 (45 bar + 70 plates)
      expect(result[1].warmUpWeightSet.Lb).toBe(115)
    })
  })

  describe('edge cases', () => {
    it('should handle exercises with very low reps', () => {
      const recommendation = createMockRecommendation({
        WarmupsCount: 2,
        Weight: { Lb: 300, Kg: 136.08 },
        Reps: 3,
      })
      const exercise = createMockExercise({ IsBodyweight: false })

      const result = computeWarmups(recommendation, exercise)

      // Should still generate valid warmups
      expect(result).toHaveLength(2)
      result.forEach((warmup) => {
        expect(warmup.warmUpReps).toBeGreaterThan(0)
      })
    })

    it('should handle missing weight data', () => {
      const recommendation = createMockRecommendation({
        WarmupsCount: 2,
        Weight: { Lb: 0, Kg: 0 },
        Reps: 10,
        IsBodyweight: false,
      })
      const exercise = createMockExercise({ IsBodyweight: false })

      const result = computeWarmups(recommendation, exercise)

      // Should return empty array or handle gracefully
      expect(result).toEqual([])
    })
  })
})

describe('WarmupCalculator (New Implementation)', () => {
  describe('Weighted Exercise Warmups', () => {
    it('should calculate 3 warmup sets for bench press', () => {
      const config: WarmupCalculationConfig = {
        warmupsCount: 3,
        workingWeight: { Kg: 100, Lb: 220 },
        workingReps: 10,
        incrementValue: 2.5,
        isPlateAvailable: true,
        isBodyweight: false,
        barbellWeight: 20,
        availablePlates: 'all',
        userBodyWeight: 80,
        isKg: true
      }

      const warmups = WarmupCalculator.computeWarmups(config)

      expect(warmups).toHaveLength(3)

      // First warmup should be bodyweight only
      expect(warmups[0].warmUpWeightSet.Kg).toBe(0)
      expect(warmups[0].WarmUpWeightSet.Kg).toBe(0)

      // Second warmup should be between 50% and 85% (actual progression)
      expect(warmups[1].warmUpWeightSet.Kg).toBeGreaterThan(50)
      expect(warmups[1].warmUpWeightSet.Kg).toBeLessThan(85)

      // Third warmup ~85% of working weight
      expect(warmups[2].warmUpWeightSet.Kg).toBeCloseTo(85, 0)

      // Reps should decrease
      expect(warmups[0].warmUpReps).toBeGreaterThan(warmups[2].warmUpReps)
    })

    it('should apply minimum reps for weighted exercises', () => {
      const config: WarmupCalculationConfig = {
        warmupsCount: 2,
        workingWeight: { Kg: 200, Lb: 440 },
        workingReps: 3, // Low reps
        incrementValue: 2.5,
        isPlateAvailable: false,
        isBodyweight: false,
        barbellWeight: 20,
        availablePlates: '',
        userBodyWeight: 80,
        isKg: true
      }

      const warmups = WarmupCalculator.computeWarmups(config)

      // Should enforce minimum 3 reps for weighted exercises
      warmups.forEach((warmup, index) => {
        if (index > 0) { // Skip first bodyweight warmup
          expect(warmup.warmUpReps).toBeGreaterThanOrEqual(3)
        }
      })
    })
  })

  describe('Bodyweight Exercise Warmups', () => {
    it('should calculate warmups for push-ups', () => {
      const config: WarmupCalculationConfig = {
        warmupsCount: 2,
        workingWeight: { Kg: 80, Lb: 176 }, // User bodyweight
        workingReps: 20,
        incrementValue: 1,
        isPlateAvailable: false,
        isBodyweight: true,
        barbellWeight: 0,
        availablePlates: '',
        userBodyWeight: 80,
        isKg: true
      }

      const warmups = WarmupCalculator.computeWarmups(config)

      expect(warmups).toHaveLength(2)

      // Weight should remain constant (bodyweight)
      expect(warmups[0].warmUpWeightSet.Kg).toBe(80)
      expect(warmups[1].warmUpWeightSet.Kg).toBe(80)

      // Reps should start at 40% and progress to 60%
      expect(warmups[0].warmUpReps).toBeCloseTo(8, 0) // 40% of 20
      expect(warmups[1].warmUpReps).toBeCloseTo(12, 0) // 60% of 20
    })
  })

  describe('Edge Cases', () => {
    it('should return empty array for zero warmups', () => {
      const config: WarmupCalculationConfig = {
        warmupsCount: 0,
        workingWeight: { Kg: 100, Lb: 220 },
        workingReps: 10,
        incrementValue: 2.5,
        isPlateAvailable: false,
        isBodyweight: false,
        barbellWeight: 20,
        availablePlates: '',
        userBodyWeight: 80,
        isKg: true
      }

      const warmups = WarmupCalculator.computeWarmups(config)

      expect(warmups).toEqual([])
    })

    it('should handle missing weight data for non-bodyweight exercises', () => {
      const config: WarmupCalculationConfig = {
        warmupsCount: 2,
        workingWeight: { Kg: 0, Lb: 0 },
        workingReps: 10,
        incrementValue: 2.5,
        isPlateAvailable: false,
        isBodyweight: false,
        barbellWeight: 20,
        availablePlates: '',
        userBodyWeight: 80,
        isKg: true
      }

      const warmups = WarmupCalculator.computeWarmups(config)

      expect(warmups).toEqual([])
    })

    it('should respect min and max weight constraints', () => {
      const config: WarmupCalculationConfig = {
        warmupsCount: 3,
        workingWeight: { Kg: 100, Lb: 220 },
        workingReps: 10,
        incrementValue: 2.5,
        minWeight: 30,
        maxWeight: 80,
        isPlateAvailable: false,
        isBodyweight: false,
        barbellWeight: 20,
        availablePlates: '',
        userBodyWeight: 80,
        isKg: true
      }

      const warmups = WarmupCalculator.computeWarmups(config)

      warmups.forEach(warmup => {
        if (warmup.warmUpWeightSet.Kg > 0) {
          expect(warmup.warmUpWeightSet.Kg).toBeGreaterThanOrEqual(30)
          expect(warmup.warmUpWeightSet.Kg).toBeLessThanOrEqual(80)
        }
      })
    })
  })

  describe('Unit Conversions', () => {
    it('should properly convert between kg and lb', () => {
      const config: WarmupCalculationConfig = {
        warmupsCount: 1,
        workingWeight: { Kg: 100, Lb: 220.46 },
        workingReps: 10,
        incrementValue: 2.5,
        isPlateAvailable: false,
        isBodyweight: false,
        barbellWeight: 20,
        availablePlates: '',
        userBodyWeight: 80,
        isKg: true
      }

      const warmups = WarmupCalculator.computeWarmups(config)

      expect(warmups).toHaveLength(1)
      // Check that kg to lb conversion is correct (approximately 2.20462)
      if (warmups[0].warmUpWeightSet.Kg > 0) {
        const expectedLb = warmups[0].warmUpWeightSet.Kg * 2.20462
        expect(warmups[0].warmUpWeightSet.Lb).toBeCloseTo(expectedLb, 1)
      }
    })
  })
})
