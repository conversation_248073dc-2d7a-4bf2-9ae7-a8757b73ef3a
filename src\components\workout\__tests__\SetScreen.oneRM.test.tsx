import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { SetScreen } from '../SetScreen'
import { NavigationProvider } from '@/contexts/NavigationContext'
import { useSetScreenLogic } from '@/hooks/useSetScreenLogic'
import { useOneRMTracking } from '@/hooks/useOneRMTracking'
import type { RecommendationModel, ExerciseModel } from '@/types/api'

// Mock dependencies
vi.mock('@/contexts/NavigationContext', () => ({
  useNavigation: () => ({
    setTitle: vi.fn(),
  }),
  NavigationProvider: ({ children }: { children: React.ReactNode }) => children,
}))

vi.mock('@/hooks/useSetScreenLogic')
vi.mock('@/hooks/useOneRMTracking')

// Mock user body weight for calculations
const mockUserBodyWeight = 80 // kg

// Mock recommendation and exercise data
const mockRecommendation: RecommendationModel = {
  Series: 3,
  Reps: 10,
  Weight: { Lb: 198, Kg: 90 },
  OneRMProgress: 0,
  RecommendationInKg: 90,
  OneRMPercentage: 75,
  WarmUpReps1: 5,
  WarmUpReps2: 3,
  WarmUpWeightSet1: { Lb: 95, Kg: 43 },
  WarmUpWeightSet2: { Lb: 115, Kg: 52 },
  WarmUpsList: [],
  WarmupsCount: 2,
  RpRest: 120,
  NbPauses: 0,
  NbRepsPauses: 0,
  IsEasy: false,
  IsMedium: true,
  IsBodyweight: false,
  Increments: { Lb: 5, Kg: 2.5 },
  Max: { Lb: 405, Kg: 183.7 },
  Min: { Lb: 45, Kg: 20.4 },
  IsNormalSets: true,
  IsDeload: false,
  IsBackOffSet: false,
  BackOffSetWeight: { Lb: 0, Kg: 0 },
  IsMaxChallenge: false,
  IsLightSession: false,
  FirstWorkSetReps: 10,
  FirstWorkSetWeight: { Lb: 198, Kg: 90 },
  FirstWorkSet1RM: { Lb: 264, Kg: 120 },
  IsPyramid: false,
  IsReversePyramid: false,
  HistorySet: [],
  ReferenceSetHistory: {
    RepetitionNumber: 10,
    Weight: { Lb: 198, Kg: 90 },
    IsWarmups: false,
    Id: '1',
    ExerciseId: 1,
    IsBodyweight: false,
    IsUnilateral: false,
    ProgramLabel: 'Test Program',
    WorkoutId: '1',
    ExerciseLabel: 'Bench Press',
    LocalDate: new Date(),
    SessionDate: new Date(),
    IsTimeBased: false,
    Seconds: 0,
    TimeSpan: '',
    State: 'Valid',
    RIR: 2,
    IsBackOffSet: false,
    IsFinished: true,
  },
  MinReps: 8,
  MaxReps: 12,
  isPlateAvailable: true,
  isDumbbellAvailable: true,
  isPulleyAvailable: false,
  isBandsAvailable: false,
  Speed: 2,
  IsManual: false,
  ReferenseReps: 10,
  ReferenseWeight: { Lb: 198, Kg: 90 },
  IsDropSet: false,
}

const mockExercise: ExerciseModel = {
  Id: 1,
  Label: 'Bench Press',
  IsSystemExercise: true,
  IsSwapTarget: false,
  IsFinished: false,
  IsUnilateral: false,
  IsTimeBased: false,
  IsEasy: false,
  IsMedium: true,
  IsBodyweight: false,
  VideoUrl: '',
  IsNextExercise: false,
  IsPlate: true,
  IsWeighted: false,
  IsPyramid: false,
  IsNormalSets: true,
  LocalVideo: '',
  IsAssisted: false,
}

const mockSetScreenLogic = {
  currentExercise: mockExercise,
  exercises: [mockExercise],
  currentExerciseIndex: 0,
  isWarmup: false,
  totalSets: 3,
  currentSetIndex: 0,
  setData: {
    reps: 10,
    weight: 100,
    duration: 0,
  },
  isSaving: false,
  saveError: null,
  showRIRPicker: false,
  showComplete: false,
  showExerciseComplete: false,
  isTransitioning: false,
  showSetSaved: false,
  recommendation: mockRecommendation,
  isLoading: false,
  error: null,
  isLastExercise: false,
  completedSets: [],
  setSetData: vi.fn(),
  handleSaveSet: vi.fn(),
  handleRIRSelect: vi.fn(),
  handleRIRCancel: vi.fn(),
  refetchRecommendation: vi.fn(),
  performancePercentage: vi.fn(() => '75%'),
  handleSetClick: vi.fn(),
}

const mockOneRMTracking = {
  currentOneRM: 133.3,
  oneRMProgress: 10.5,
  progressMessage: '+10.5%',
  lastTimeInfo: 'Last time: 10 x 90 kg',
  handleWeightChange: vi.fn(),
  handleRepsChange: vi.fn(),
}

describe('SetScreen - 1RM Integration', () => {
  const user = userEvent.setup()

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useSetScreenLogic).mockReturnValue(mockSetScreenLogic)
    vi.mocked(useOneRMTracking).mockReturnValue(mockOneRMTracking)
  })

  it('should display 1RM progress when weight/reps are entered', async () => {
    render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    // Should display progress message
    expect(screen.getByText('1RM Progress: +10.5%')).toBeInTheDocument()

    // Should display last time info
    expect(screen.getByText('Last time: 10 x 90 kg')).toBeInTheDocument()
  })

  it('should update 1RM when weight changes', async () => {
    const { rerender } = render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    // Find weight input
    const weightInput = screen.getByLabelText('Weight')

    // Change weight
    await user.clear(weightInput)
    await user.type(weightInput, '110')

    // Verify handleWeightChange was called
    await waitFor(() => {
      expect(mockOneRMTracking.handleWeightChange).toHaveBeenCalled()
    })

    // Update mock to reflect new 1RM
    vi.mocked(useOneRMTracking).mockReturnValue({
      ...mockOneRMTracking,
      currentOneRM: 146.63,
      oneRMProgress: 15.2,
      progressMessage: '+15.2%',
    })

    rerender(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    // Should display updated progress
    expect(screen.getByText('1RM Progress: +15.2%')).toBeInTheDocument()
  })

  it('should update 1RM when reps change', async () => {
    render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    // Find reps input
    const repsInput = screen.getByLabelText('Reps')

    // Change reps
    await user.clear(repsInput)
    await user.type(repsInput, '12')

    // Verify handleRepsChange was called
    await waitFor(() => {
      expect(mockOneRMTracking.handleRepsChange).toHaveBeenCalled()
    })
  })

  it('should handle bodyweight exercises correctly', async () => {
    const bodyweightExercise = {
      ...mockExercise,
      IsBodyweight: true,
      Label: 'Pull-ups',
    }

    vi.mocked(useSetScreenLogic).mockReturnValue({
      ...mockSetScreenLogic,
      currentExercise: bodyweightExercise,
      setData: { reps: 10, weight: 20, duration: 0 }, // 20kg additional weight
    })

    vi.mocked(useOneRMTracking).mockReturnValue({
      ...mockOneRMTracking,
      currentOneRM: 133.3, // Based on total weight (80 + 20 = 100kg)
      progressMessage: '+8.5%',
    })

    render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    // Should display "Additional Weight" label for bodyweight exercises
    expect(screen.getByText('Additional Weight')).toBeInTheDocument()

    // Should display progress based on total weight
    expect(screen.getByText('1RM Progress: +8.5%')).toBeInTheDocument()
  })

  it('should display 1RM for warmup sets', async () => {
    vi.mocked(useSetScreenLogic).mockReturnValue({
      ...mockSetScreenLogic,
      isWarmup: true,
    })

    vi.mocked(useOneRMTracking).mockReturnValue({
      ...mockOneRMTracking,
      progressMessage: '+5.0%', // Show progress for warmups too
      lastTimeInfo: 'Last time: 5 x 43 kg',
    })

    render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    // Should display progress for warmup sets
    expect(screen.getByText('1RM Progress: +5.0%')).toBeInTheDocument()
  })

  it('should display negative progress correctly', async () => {
    vi.mocked(useOneRMTracking).mockReturnValue({
      ...mockOneRMTracking,
      oneRMProgress: -5.5,
      progressMessage: '-5.5%',
    })

    render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    // Should display negative progress
    expect(screen.getByText('1RM Progress: -5.5%')).toBeInTheDocument()
  })

  it('should integrate with SetInputs component', async () => {
    render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    // Check that weight and reps inputs are rendered
    expect(screen.getByLabelText('Weight')).toBeInTheDocument()
    expect(screen.getByLabelText('Reps')).toBeInTheDocument()

    // Verify they have correct values
    expect(screen.getByLabelText('Weight')).toHaveValue(100)
    expect(screen.getByLabelText('Reps')).toHaveValue(10)
  })

  it('should pass correct props to useOneRMTracking hook', () => {
    render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    // Verify useOneRMTracking was called with correct props
    expect(useOneRMTracking).toHaveBeenCalledWith({
      weight: 100,
      reps: 10,
      exercise: mockExercise,
      recommendation: mockRecommendation,
      unit: 'kg', // Default unit
      userBodyWeight: mockUserBodyWeight,
      isFirstWorkSet: false, // Warmup sets present, so first work set is not index 0
      onWeightChange: expect.any(Function),
      onRepsChange: expect.any(Function),
    })
  })

  it('should handle missing recommendation gracefully', async () => {
    vi.mocked(useSetScreenLogic).mockReturnValue({
      ...mockSetScreenLogic,
      recommendation: null,
    })

    vi.mocked(useOneRMTracking).mockReturnValue({
      ...mockOneRMTracking,
      progressMessage: '',
      lastTimeInfo: '',
    })

    render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    // Should still render without errors
    expect(screen.getByLabelText('Weight')).toBeInTheDocument()
    expect(screen.getByLabelText('Reps')).toBeInTheDocument()

    // No progress shown without recommendation
    expect(screen.queryByText(/\+.*%/)).not.toBeInTheDocument()
  })

  it('should show 1RM info in mobile-optimized layout', async () => {
    render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    // Find the 1RM display area
    const oneRMDisplay = screen.getByTestId('one-rm-display')

    // Should have mobile-friendly styling
    expect(oneRMDisplay).toHaveClass('bg-brand-primary/10', 'p-3', 'rounded-lg')

    // Should be prominently displayed
    expect(oneRMDisplay).toBeVisible()
  })
})
