'use client'

import React, { useEffect } from 'react'
import { useNavigation } from '@/contexts/NavigationContext'
import { useSetScreenLogic } from '@/hooks/useSetScreenLogic'
import { useOneRMTracking } from '@/hooks/useOneRMTracking'
import { useAuthStore } from '@/stores/authStore'
import { SetInputs } from './SetInputs-refactored'
import { RIRPicker } from './RIRPicker'
import { ExerciseCompleteView } from './ExerciseCompleteView'
import { WorkoutCompleteView } from './WorkoutCompleteView'
import { SetScreenLoadingState } from './SetScreenLoadingState'
import { SetScreenErrorState } from './SetScreenErrorState'
import { SetProgressInfo } from './SetProgressInfo'
import { ExerciseInfo } from './ExerciseInfo'
import { SaveErrorDisplay } from './SaveErrorDisplay'
import { WarmupSetDisplay } from './WarmupSetDisplay'
import { SetListMobile } from './SetListMobile'
import { SaveSetButton } from './SaveSetButton'
import { debugLog } from '@/utils/debugLog'
import { getErrorMessage } from '@/utils/errorMessages'
import { computeWarmups } from '@/utils/warmupCalculator'
import { useSetListMobile } from '@/hooks/useSetListMobile'
import type { MassUnit } from '@/types/api/WorkoutLogSerieModelRef'

interface SetScreenProps {
  exerciseId?: number
}

export function SetScreen({ exerciseId }: SetScreenProps) {
  const { setTitle } = useNavigation()
  const {
    // State
    currentExercise,
    exercises,
    currentExerciseIndex,
    isWarmup,
    totalSets,
    currentSetIndex,
    setData,
    isSaving,
    saveError,
    showRIRPicker,
    showComplete,
    showExerciseComplete,
    isTransitioning,
    showSetSaved,
    recommendation,
    isLoading,
    error,
    isLastExercise,

    // Actions
    setSetData,
    handleSaveSet,
    handleRIRSelect,
    handleRIRCancel,
    refetchRecommendation,
    performancePercentage,
    handleSetClick,
  } = useSetScreenLogic(exerciseId)

  // Get user preferences
  const { getCachedUserInfo } = useAuthStore()
  const userInfo = getCachedUserInfo()

  // Get user mass unit preference
  const userMassUnit = (userInfo?.MassUnit || 'kg') as MassUnit

  const { exerciseWorkSets, massUnit } = useSetListMobile(
    currentExercise || null,
    recommendation,
    userMassUnit
  )

  // Update navigation title with exercise name
  useEffect(() => {
    if (currentExercise) {
      setTitle(currentExercise.Label)
    }
  }, [currentExercise, setTitle])

  // Log state for debugging set index issue
  useEffect(() => {
    debugLog('[SetScreen] Component mounted/updated:', {
      exerciseId,
      currentSetIndex,
      totalSets,
      currentExerciseIndex,
      currentExerciseLabel: currentExercise?.Label,
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [exerciseId, currentSetIndex, totalSets, currentExerciseIndex])

  // Get user body weight for 1RM tracking
  let userBodyWeight = 80 // Default kg
  if (massUnit === 'kg') {
    userBodyWeight =
      userInfo?.BodyWeight &&
      typeof userInfo.BodyWeight === 'object' &&
      'Kg' in userInfo.BodyWeight
        ? Number(userInfo.BodyWeight.Kg)
        : 80
  } else {
    userBodyWeight =
      userInfo?.BodyWeight &&
      typeof userInfo.BodyWeight === 'object' &&
      'Lb' in userInfo.BodyWeight
        ? Number(userInfo.BodyWeight.Lb)
        : 176
  }

  // Calculate warmup sets
  const warmupSets =
    recommendation && currentExercise
      ? computeWarmups(recommendation, currentExercise)
      : []

  // 1RM Tracking
  const {
    progressMessage,
    lastTimeInfo,
    handleWeightChange,
    handleRepsChange,
  } = useOneRMTracking({
    weight: setData.weight,
    reps: setData.reps,
    exercise: currentExercise || null,
    recommendation,
    unit: massUnit === 'lb' ? 'lbs' : 'kg',
    userBodyWeight,
    isFirstWorkSet: !isWarmup && currentSetIndex === warmupSets.length,
    onWeightChange: (weight) => setSetData((prev) => ({ ...prev, weight })),
    onRepsChange: (reps) => setSetData((prev) => ({ ...prev, reps })),
  })

  // Debug logging for 1RM
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      debugLog('[SetScreen] 1RM Debug:', {
        isWarmup,
        currentSetIndex,
        warmupSetsLength: warmupSets.length,
        isFirstWorkSet:
          !isWarmup && currentSetIndex === (warmupSets.length || 0),
        weight: setData.weight,
        reps: setData.reps,
        progressMessage,
        lastTimeInfo,
        massUnit,
        userBodyWeight,
        hasUserInfo: !!userInfo,
        recommendation: {
          hasFirstWorkSet1RM: !!recommendation?.FirstWorkSet1RM,
          firstWorkSet1RM: recommendation?.FirstWorkSet1RM,
          oneRMProgress: recommendation?.OneRMProgress,
        },
      })
    }
  }, [
    isWarmup,
    currentSetIndex,
    warmupSets.length,
    setData.weight,
    setData.reps,
    progressMessage,
    lastTimeInfo,
    massUnit,
    userBodyWeight,
    userInfo,
    recommendation,
  ])

  // Loading state
  if (isLoading && !recommendation) {
    return <SetScreenLoadingState />
  }

  // Error state
  if (error && !recommendation) {
    return <SetScreenErrorState onRetry={refetchRecommendation} />
  }

  // Exercise complete state
  if (showExerciseComplete) {
    return (
      <ExerciseCompleteView
        exerciseLabel={currentExercise?.Label}
        isLastExercise={isLastExercise}
        nextExerciseLabel={
          exercises && typeof currentExerciseIndex === 'number'
            ? exercises[currentExerciseIndex + 1]?.Label
            : undefined
        }
      />
    )
  }

  // Workout complete state
  if (showComplete) {
    return <WorkoutCompleteView />
  }

  return (
    <div className="min-h-[100dvh] bg-bg-primary flex flex-col">
      {/* Scrollable content area */}
      <div className="flex-1 overflow-y-auto pb-24">
        {/* Error Display */}
        {error && (
          <div className="bg-error/10 border-b border-error/20 px-4 py-3">
            <p className="text-error text-sm">{getErrorMessage(error)}</p>
          </div>
        )}

        {/* Exercise Name at the Top */}
        <div className="px-4 py-4 bg-bg-secondary border-b border-border-primary">
          <h1 className="text-2xl font-bold text-text-primary">
            {currentExercise?.Label || 'Exercise'}
          </h1>
        </div>

        {/* Set Progress Info */}
        <SetProgressInfo
          isWarmup={isWarmup}
          currentSetIndex={currentSetIndex}
          totalSets={totalSets}
          showSetSaved={showSetSaved}
        />

        {/* Warmup Sets Display */}
        {warmupSets.length > 0 && (
          <WarmupSetDisplay
            warmupSets={warmupSets}
            isBodyweight={currentExercise?.IsBodyweight || false}
          />
        )}

        {/* Exercise Info */}
        <ExerciseInfo
          currentExercise={currentExercise}
          recommendation={recommendation}
          performancePercentage={performancePercentage()}
          oneRMProgress={progressMessage}
          lastTimeInfo={lastTimeInfo}
        />

        {/* All Sets Display - Mobile Version */}
        {exerciseWorkSets && (
          <div className="py-6">
            <SetListMobile
              exercise={exerciseWorkSets}
              onSetTap={(_set, index) => {
                handleSetClick(index)
              }}
              massUnit={massUnit}
            />
          </div>
        )}

        {/* Set Inputs */}
        <div className="px-4 py-6">
          <SetInputs
            reps={setData.reps}
            weight={setData.weight}
            duration={setData.duration}
            unit={massUnit === 'lb' ? 'lbs' : 'kg'}
            onChange={(data) => {
              // Use 1RM handlers for weight and reps changes
              // Handle each change independently to support simultaneous updates

              if (data.weight !== undefined && data.weight !== setData.weight) {
                handleWeightChange(data.weight.toString())
              }

              if (data.reps !== undefined && data.reps !== setData.reps) {
                handleRepsChange(data.reps.toString())
              }

              // Only handle duration separately
              if (data.duration !== undefined) {
                const { duration } = data
                setSetData((prev) => ({ ...prev, duration }))
              }
            }}
            disabled={isSaving || isTransitioning}
            isBodyweight={currentExercise?.IsBodyweight}
            isTimeBased={currentExercise?.IsTimeBased}
          />
        </div>

        {/* Save error */}
        {saveError && (
          <SaveErrorDisplay error={saveError} onRetry={handleSaveSet} />
        )}
      </div>

      {/* Floating Save Button */}
      <SaveSetButton
        onClick={handleSaveSet}
        isSaving={isSaving}
        isTransitioning={isTransitioning}
      />

      {/* RIR Picker */}
      <RIRPicker
        isOpen={showRIRPicker}
        onSelect={handleRIRSelect}
        onCancel={handleRIRCancel}
      />
    </div>
  )
}
