import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { SetScreen } from '../SetScreen'
import { NavigationProvider } from '@/contexts/NavigationContext'
import { useAuthStore } from '@/stores/authStore'
import { useSetScreenLogic } from '@/hooks/useSetScreenLogic'
import { useOneRMTracking } from '@/hooks/useOneRMTracking'

// Mock dependencies
vi.mock('@/stores/authStore')
vi.mock('@/hooks/useSetScreenLogic')
vi.mock('@/hooks/useOneRMTracking')
vi.mock('@/utils/debugLog', () => ({
  debugLog: vi.fn(),
}))

// Mock Next.js navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
  usePathname: () => '/workout',
}))

const mockRecommendation = {
  ExerciseId: 1,
  Label: 'Bench Press',
  IsBodyweight: false,
  IsAssisted: false,
  IsWeighted: false,
  FirstWorkSetWeight: { Lb: 220, Kg: 100 },
  FirstWorkSetReps: 8,
  FirstWorkSet1RM: { Lb: 275, Kg: 125 },
  Weight: { Lb: 220, Kg: 100 },
  OneRMProgress: 0,
  RecommendationInKg: 100,
  WarmupsCount: 2,
  WarmUpsList: [],
}

const mockUserInfo = {
  MassUnit: 'lbs',
  BodyWeight: { Lb: 176, Kg: 80 },
}

describe('SetScreen - User Preferences', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(useAuthStore as any).mockReturnValue({
      getCachedUserInfo: () => mockUserInfo,
    })
    ;(useOneRMTracking as any).mockReturnValue({
      progressMessage: '0%',
      lastTimeInfo: 'Last time: 8 x 220 lbs',
      handleWeightChange: vi.fn(),
      handleRepsChange: vi.fn(),
    })
    ;(useSetScreenLogic as any).mockReturnValue({
      currentExercise: mockRecommendation,
      recommendation: mockRecommendation,
      setData: { weight: 100, reps: 8 },
      setSetData: vi.fn(),
      isWarmup: false,
      currentSetIndex: 2,
      totalSets: 5,
      error: null,
      isLoading: false,
      isSaving: false,
      saveError: null,
      isTransitioning: false,
      showRIRPicker: false,
      showComplete: false,
      showExerciseComplete: false,
      showSetSaved: false,
      handleSaveSet: vi.fn(),
      handleRIRSelect: vi.fn(),
      handleRIRCancel: vi.fn(),
      refetchRecommendation: vi.fn(),
      performancePercentage: () => 100,
    })
  })

  it('should use user mass unit preference from cached user info', () => {
    render(
      <NavigationProvider>
        <SetScreen />
      </NavigationProvider>
    )

    // Check that the weight is displayed in lbs (user preference)
    expect(screen.getByText(/Last time: 8 x 220 lbs/)).toBeInTheDocument()
  })

  it('should use user body weight from cached user info', () => {
    ;(useSetScreenLogic as any).mockReturnValue({
      currentExercise: {
        ...mockRecommendation,
        Label: 'Pull-ups',
        IsBodyweight: true,
      },
      recommendation: {
        ...mockRecommendation,
        Label: 'Pull-ups',
        IsBodyweight: true,
      },
      setData: { weight: 0, reps: 8 },
      setSetData: vi.fn(),
      isWarmup: false,
      currentSetIndex: 2,
      totalSets: 5,
      error: null,
      isLoading: false,
      isSaving: false,
      saveError: null,
      isTransitioning: false,
      showRIRPicker: false,
      showComplete: false,
      showExerciseComplete: false,
      showSetSaved: false,
      handleSaveSet: vi.fn(),
      handleRIRSelect: vi.fn(),
      handleRIRCancel: vi.fn(),
      refetchRecommendation: vi.fn(),
      performancePercentage: () => 100,
    })

    render(
      <NavigationProvider>
        <SetScreen />
      </NavigationProvider>
    )

    // For bodyweight exercises, the effective weight should include user's body weight
    expect(screen.getByTestId('one-rm-display')).toBeInTheDocument()
  })

  it('should fall back to defaults when user info is not available', () => {
    ;(useAuthStore as any).mockReturnValue({
      getCachedUserInfo: () => null,
    })
    ;(useOneRMTracking as any).mockReturnValue({
      progressMessage: '0%',
      lastTimeInfo: 'Last time: 8 x 100 kg',
      handleWeightChange: vi.fn(),
      handleRepsChange: vi.fn(),
    })

    render(
      <NavigationProvider>
        <SetScreen />
      </NavigationProvider>
    )

    // Should fall back to kg (default)
    expect(screen.getByText(/Last time: 8 x 100 kg/)).toBeInTheDocument()
  })

  it('should display 1RM even when progress is 0%', () => {
    render(
      <NavigationProvider>
        <SetScreen />
      </NavigationProvider>
    )

    // Should still show 1RM display with 0% progress
    expect(screen.getByTestId('one-rm-display')).toBeInTheDocument()
    expect(screen.getByText('1RM Progress: 0%')).toBeInTheDocument()
  })
})
