import { describe, it, expect } from 'vitest'
import { createWorkoutSets } from '../createWorkoutSetsMAUI'
import type { ExerciseModel, RecommendationModel } from '@/types'

// Mock exercise data
const mockExercise: ExerciseModel = {
  Id: 1,
  Label: 'Bench Press',
  IsSystemExercise: true,
  IsSwapTarget: false,
  IsFinished: false,
  IsUnilateral: false,
  IsTimeBased: false,
  IsEasy: false,
  IsMedium: true,
  IsBodyweight: false,
  VideoUrl: '',
  IsNextExercise: false,
  IsPlate: true,
  IsWeighted: false,
  IsPyramid: false,
  IsNormalSets: true,
  LocalVideo: '',
  IsAssisted: false,
  BodyPartId: 1,
  EquipmentId: 1,
  IsFlexibility: false,
}

// Mock recommendation data
const mockRecommendation: RecommendationModel = {
  ExerciseId: 1,
  Series: 3,
  Reps: 10,
  Weight: { Kg: 60, Lb: 132 },
  WarmupsCount: 2,
  RpRest: 120,
  NbPauses: 0,
  NbRepsPauses: 0,
  WarmUpsList: [
    { WarmUpReps: 5, WarmUpWeightSet: { Kg: 40, Lb: 88 } },
    { WarmUpReps: 3, WarmUpWeightSet: { Kg: 50, Lb: 110 } },
  ],
  FirstWorkSetReps: 10,
  FirstWorkSetWeight: { Kg: 55, Lb: 121 },
  Increments: { Kg: 2.5, Lb: 5 },
  Min: { Kg: 0, Lb: 0 },
  Max: { Kg: 200, Lb: 440 },
}

describe('createWorkoutSets', () => {
  describe('Warmup Sets', () => {
    it('should generate warmup sets with correct properties', () => {
      const sets = createWorkoutSets(mockExercise, mockRecommendation, true)
      
      const warmupSets = sets.filter(set => set.IsWarmups)
      expect(warmupSets).toHaveLength(2)
      
      // First warmup set
      expect(warmupSets[0]).toMatchObject({
        SetNo: 'W',
        Reps: 5,
        Weight: { Kg: 40, Lb: 88 },
        IsWarmups: true,
        IsHeaderCell: true,
        SetTitle: "Let's warm up:",
        IsNext: true,
        IsFinished: false,
        IsLastWarmupSet: false,
      })
      
      // Last warmup set
      expect(warmupSets[1]).toMatchObject({
        SetNo: 'W',
        Reps: 3,
        Weight: { Kg: 50, Lb: 110 },
        IsWarmups: true,
        IsHeaderCell: false,
        SetTitle: 'Last warm-up set:',
        IsNext: false,
        IsFinished: false,
        IsLastWarmupSet: true,
      })
    })
  })

  describe('Normal Sets', () => {
    it('should generate normal work sets with correct properties', () => {
      const sets = createWorkoutSets(mockExercise, mockRecommendation, true)
      
      const workSets = sets.filter(set => !set.IsWarmups)
      expect(workSets).toHaveLength(3)
      
      // First work set
      expect(workSets[0]).toMatchObject({
        SetNo: '1',
        Reps: 10,
        Weight: { Kg: 60, Lb: 132 },
        IsWarmups: false,
        IsFirstWorkSet: true,
        IsNormalset: true,
        SetTitle: 'Working sets:',
        LastTimeSet: 'Last time: 10 x 55 kg',
        IsFinished: false,
      })
      
      // Subsequent work sets
      expect(workSets[1]).toMatchObject({
        SetNo: '2',
        Reps: 10,
        Weight: { Kg: 60, Lb: 132 },
        IsWarmups: false,
        IsFirstWorkSet: false,
        IsNormalset: true,
        SetTitle: '',
        LastTimeSet: '',
        IsFinished: false,
      })
    })
  })

  describe('Pyramid Sets', () => {
    it('should generate pyramid sets with increasing weight and decreasing reps', () => {
      const pyramidReco = {
        ...mockRecommendation,
        IsPyramid: true,
        Series: 3,
      }
      
      const sets = createWorkoutSets(mockExercise, pyramidReco, true)
      const workSets = sets.filter(set => !set.IsWarmups)
      
      expect(workSets).toHaveLength(3)
      
      // First set - base weight and reps
      expect(workSets[0]).toMatchObject({
        Weight: { Kg: 60, Lb: 132 },
        Reps: 10,
        SetTitle: 'Pyramid set:',
      })
      
      // Second set - 10% more weight, 2 less reps
      expect(workSets[1].Weight.Kg).toBeGreaterThan(60)
      expect(workSets[1].Reps).toBe(8)
      
      // Third set - even more weight, even less reps
      expect(workSets[2].Weight.Kg).toBeGreaterThan(workSets[1].Weight.Kg)
      expect(workSets[2].Reps).toBe(6)
    })
  })

  describe('Reverse Pyramid Sets', () => {
    it('should generate reverse pyramid sets with decreasing weight and increasing reps', () => {
      const reversePyramidReco = {
        ...mockRecommendation,
        IsReversePyramid: true,
        Series: 3,
      }
      
      const sets = createWorkoutSets(mockExercise, reversePyramidReco, true)
      const workSets = sets.filter(set => !set.IsWarmups)
      
      expect(workSets).toHaveLength(3)
      
      // First set - heaviest weight
      expect(workSets[0]).toMatchObject({
        Weight: { Kg: 60, Lb: 132 },
        Reps: 10,
        SetTitle: 'Reverse pyramid:',
      })
      
      // Second set - 10% less weight, 2 more reps
      expect(workSets[1].Weight.Kg).toBeLessThan(60)
      expect(workSets[1].Reps).toBe(12)
      
      // Third set - even less weight, even more reps
      expect(workSets[2].Weight.Kg).toBeLessThan(workSets[1].Weight.Kg)
      expect(workSets[2].Reps).toBe(14)
    })
  })

  describe('Back-off Sets', () => {
    it('should generate back-off sets with lighter weight after main set', () => {
      const backOffReco = {
        ...mockRecommendation,
        IsBackOffSet: true,
        Series: 3,
      }
      
      const sets = createWorkoutSets(mockExercise, backOffReco, true)
      const workSets = sets.filter(set => !set.IsWarmups)
      
      expect(workSets).toHaveLength(3)
      
      // First set - main set
      expect(workSets[0]).toMatchObject({
        Weight: { Kg: 60, Lb: 132 },
        Reps: 10,
        SetTitle: '',
        IsBackOffSet: false,
      })
      
      // Back-off sets - 20% lighter, 2 more reps
      expect(workSets[1]).toMatchObject({
        Reps: 12,
        SetTitle: 'Back-off set:',
        IsBackOffSet: true,
      })
      expect(workSets[1].Weight.Kg).toBe(48) // 60 * 0.8 = 48
      expect(workSets[1].Weight.Lb).toBeCloseTo(105.82, 2) // Floating point precision
    })
  })

  describe('Rest-Pause Sets', () => {
    it('should generate single rest-pause set', () => {
      const restPauseReco = {
        ...mockRecommendation,
        NbPauses: 3,
        Series: 1,
      }
      
      const sets = createWorkoutSets(mockExercise, restPauseReco, true)
      const workSets = sets.filter(set => !set.IsWarmups)
      
      expect(workSets).toHaveLength(1)
      
      expect(workSets[0]).toMatchObject({
        Weight: { Kg: 60, Lb: 132 },
        Reps: 10,
        SetTitle: 'Rest-pause',
        NbPause: 3,
        IsFirstWorkSet: true,
        IsLastSet: true,
      })
    })
  })

  describe('Set Properties', () => {
    it('should set IsNext for first unfinished set', () => {
      const sets = createWorkoutSets(mockExercise, mockRecommendation, true)
      
      const nextSets = sets.filter(set => set.IsNext)
      expect(nextSets).toHaveLength(1)
      expect(nextSets[0]).toBe(sets[0]) // First warmup set should be next
    })

    it('should inherit exercise properties', () => {
      const sets = createWorkoutSets(mockExercise, mockRecommendation, true)
      
      sets.forEach(set => {
        expect(set.IsBodyweight).toBe(mockExercise.IsBodyweight)
        expect(set.IsTimeBased).toBe(mockExercise.IsTimeBased)
        expect(set.IsUnilateral).toBe(mockExercise.IsUnilateral)
        expect(set.IsFlexibility).toBe(mockExercise.IsFlexibility)
        expect(set.BodypartId).toBe(mockExercise.BodyPartId)
        expect(set.EquipmentId).toBe(mockExercise.EquipmentId)
      })
    })

    it('should set increments and limits from recommendation', () => {
      const sets = createWorkoutSets(mockExercise, mockRecommendation, true)
      
      sets.forEach(set => {
        expect(set.Increments).toEqual(mockRecommendation.Increments)
        expect(set.Min).toEqual(mockRecommendation.Min)
        expect(set.Max).toEqual(mockRecommendation.Max)
      })
    })
  })
})
