import type { WorkoutLogSerieModelRef } from '@/types/api/WorkoutLogSerieModelRef'

/**
 * Set State Management Utilities
 * Matches MAUI set state management logic
 */

/**
 * Update IsNext property for all sets in an exercise
 * Matches MAUI logic for determining which set is next
 */
export function updateNextSetStates(sets: WorkoutLogSerieModelRef[]): WorkoutLogSerieModelRef[] {
  // Clear all IsNext flags first
  const updatedSets = sets.map(set => ({ ...set, IsNext: false }))
  
  // Find first unfinished set and mark as next
  const firstUnfinishedIndex = updatedSets.findIndex(set => !set.IsFinished)
  if (firstUnfinishedIndex !== -1) {
    updatedSets[firstUnfinishedIndex].IsNext = true
  }
  
  return updatedSets
}

/**
 * Mark a set as completed and update states
 * Matches MAUI set completion logic
 */
export function completeSet(
  sets: WorkoutLogSerieModelRef[], 
  setIndex: number
): WorkoutLogSerieModelRef[] {
  if (setIndex < 0 || setIndex >= sets.length) {
    return sets
  }
  
  const updatedSets = [...sets]
  updatedSets[setIndex] = {
    ...updatedSets[setIndex],
    IsFinished: true,
    IsNext: false
  }
  
  // Update IsNext for remaining sets
  return updateNextSetStates(updatedSets)
}

/**
 * Mark a set as incomplete and update states
 */
export function uncompleteSet(
  sets: WorkoutLogSerieModelRef[], 
  setIndex: number
): WorkoutLogSerieModelRef[] {
  if (setIndex < 0 || setIndex >= sets.length) {
    return sets
  }
  
  const updatedSets = [...sets]
  updatedSets[setIndex] = {
    ...updatedSets[setIndex],
    IsFinished: false,
    IsNext: false
  }
  
  // Update IsNext for remaining sets
  return updateNextSetStates(updatedSets)
}

/**
 * Check if all work sets (non-warmup) are completed
 */
export function areAllWorkSetsCompleted(sets: WorkoutLogSerieModelRef[]): boolean {
  const workSets = sets.filter(set => !set.IsWarmups)
  return workSets.length > 0 && workSets.every(set => set.IsFinished)
}

/**
 * Check if all sets (including warmups) are completed
 */
export function areAllSetsCompleted(sets: WorkoutLogSerieModelRef[]): boolean {
  return sets.length > 0 && sets.every(set => set.IsFinished)
}

/**
 * Get the current set index (the next set to be performed)
 */
export function getCurrentSetIndex(sets: WorkoutLogSerieModelRef[]): number {
  return sets.findIndex(set => set.IsNext)
}

/**
 * Get the next set to be performed
 */
export function getNextSet(sets: WorkoutLogSerieModelRef[]): WorkoutLogSerieModelRef | null {
  const nextSet = sets.find(set => set.IsNext)
  return nextSet || null
}

/**
 * Get exercise completion percentage
 */
export function getExerciseCompletionPercentage(sets: WorkoutLogSerieModelRef[]): number {
  if (sets.length === 0) return 0
  
  const completedSets = sets.filter(set => set.IsFinished).length
  return Math.round((completedSets / sets.length) * 100)
}

/**
 * Get work sets completion percentage (excluding warmups)
 */
export function getWorkSetsCompletionPercentage(sets: WorkoutLogSerieModelRef[]): number {
  const workSets = sets.filter(set => !set.IsWarmups)
  if (workSets.length === 0) return 0
  
  const completedWorkSets = workSets.filter(set => set.IsFinished).length
  return Math.round((completedWorkSets / workSets.length) * 100)
}

/**
 * Check if exercise can be finished (all work sets completed)
 */
export function canFinishExercise(sets: WorkoutLogSerieModelRef[]): boolean {
  return areAllWorkSetsCompleted(sets)
}

/**
 * Mark exercise as finished
 */
export function finishExercise(sets: WorkoutLogSerieModelRef[]): WorkoutLogSerieModelRef[] {
  return sets.map(set => ({
    ...set,
    IsExerciseFinished: true,
    IsNext: false
  }))
}

/**
 * Reset exercise state (mark all sets as unfinished)
 */
export function resetExercise(sets: WorkoutLogSerieModelRef[]): WorkoutLogSerieModelRef[] {
  const resetSets = sets.map(set => ({
    ...set,
    IsFinished: false,
    IsExerciseFinished: false,
    IsNext: false
  }))
  
  // Set first set as next
  return updateNextSetStates(resetSets)
}

/**
 * Add a new set to the exercise
 */
export function addSet(
  sets: WorkoutLogSerieModelRef[],
  newSet: Partial<WorkoutLogSerieModelRef>
): WorkoutLogSerieModelRef[] {
  const lastWorkSet = sets.filter(set => !set.IsWarmups).pop()
  
  if (!lastWorkSet) {
    return sets
  }
  
  // Create new set based on last work set
  const setNumber = sets.filter(set => !set.IsWarmups).length + 1
  const newSetComplete: WorkoutLogSerieModelRef = {
    ...lastWorkSet,
    ...newSet,
    SetNo: setNumber.toString(),
    IsFinished: false,
    IsNext: false,
    IsLastSet: false,
    IsFirstWorkSet: false,
    IsHeaderCell: false,
    SetTitle: '',
    LastTimeSet: '',
  }
  
  // Update IsLastSet for previous last set
  const updatedSets = sets.map(set => ({
    ...set,
    IsLastSet: false
  }))
  
  // Add new set
  updatedSets.push(newSetComplete)
  
  // Mark new set as last set
  updatedSets[updatedSets.length - 1].IsLastSet = true
  
  // Update IsNext states
  return updateNextSetStates(updatedSets)
}

/**
 * Remove a set from the exercise
 */
export function removeSet(
  sets: WorkoutLogSerieModelRef[],
  setIndex: number
): WorkoutLogSerieModelRef[] {
  if (setIndex < 0 || setIndex >= sets.length) {
    return sets
  }
  
  const updatedSets = sets.filter((_, index) => index !== setIndex)
  
  // Update set numbers for work sets
  let workSetNumber = 1
  const finalSets = updatedSets.map(set => {
    if (!set.IsWarmups) {
      return {
        ...set,
        SetNo: workSetNumber++.toString(),
        IsLastSet: false
      }
    }
    return set
  })
  
  // Mark last set
  if (finalSets.length > 0) {
    finalSets[finalSets.length - 1].IsLastSet = true
  }
  
  // Update IsNext states
  return updateNextSetStates(finalSets)
}

/**
 * Update set data (weight, reps, etc.)
 */
export function updateSetData(
  sets: WorkoutLogSerieModelRef[],
  setIndex: number,
  updates: Partial<WorkoutLogSerieModelRef>
): WorkoutLogSerieModelRef[] {
  if (setIndex < 0 || setIndex >= sets.length) {
    return sets
  }
  
  const updatedSets = [...sets]
  updatedSets[setIndex] = {
    ...updatedSets[setIndex],
    ...updates
  }
  
  return updatedSets
}

/**
 * Get set statistics
 */
export function getSetStatistics(sets: WorkoutLogSerieModelRef[]): {
  totalSets: number,
  warmupSets: number,
  workSets: number,
  completedSets: number,
  completedWorkSets: number,
  completionPercentage: number,
  workSetsCompletionPercentage: number,
  canFinish: boolean,
  allCompleted: boolean,
  allWorkSetsCompleted: boolean
} {
  const totalSets = sets.length
  const warmupSets = sets.filter(set => set.IsWarmups).length
  const workSets = sets.filter(set => !set.IsWarmups).length
  const completedSets = sets.filter(set => set.IsFinished).length
  const completedWorkSets = sets.filter(set => !set.IsWarmups && set.IsFinished).length
  
  return {
    totalSets,
    warmupSets,
    workSets,
    completedSets,
    completedWorkSets,
    completionPercentage: getExerciseCompletionPercentage(sets),
    workSetsCompletionPercentage: getWorkSetsCompletionPercentage(sets),
    canFinish: canFinishExercise(sets),
    allCompleted: areAllSetsCompleted(sets),
    allWorkSetsCompleted: areAllWorkSetsCompleted(sets)
  }
}
