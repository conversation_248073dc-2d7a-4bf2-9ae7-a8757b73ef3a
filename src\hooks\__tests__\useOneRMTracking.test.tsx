import { renderHook, act } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { useOneRMTracking } from '../useOneRMTracking'
import type { RecommendationModel, ExerciseModel } from '@/types/api'

// Mock the oneRmCalculator module
vi.mock('@/utils/oneRmCalculator', () => ({
  computeOneRM: vi.fn((weight: number, reps: number) => {
    if (reps <= 0) return weight
    if (reps === 1) return weight
    return 0.0333 * reps * weight + weight
  }),
  calculateOneRMProgress: vi.fn((lastOneRM: number, currentOneRM: number) => {
    if (lastOneRM <= 0) return 0
    return (
      Math.round((((currentOneRM - lastOneRM) * 100) / lastOneRM) * 100) / 100
    )
  }),
}))

// Mock recommendation model
const mockRecommendation: RecommendationModel = {
  Series: 3,
  Reps: 10,
  Weight: { Lb: 135, Kg: 61.23 },
  OneRMProgress: 0,
  RecommendationInKg: 61.23,
  OneRMPercentage: 75,
  WarmUpReps1: 5,
  WarmUpReps2: 3,
  WarmUpWeightSet1: { Lb: 95, Kg: 43 },
  WarmUpWeightSet2: { Lb: 115, Kg: 52 },
  WarmUpsList: [],
  WarmupsCount: 2,
  RpRest: 120,
  NbPauses: 0,
  NbRepsPauses: 0,
  IsEasy: false,
  IsMedium: true,
  IsBodyweight: false,
  Increments: { Lb: 5, Kg: 2.5 },
  Max: { Lb: 405, Kg: 183.7 },
  Min: { Lb: 45, Kg: 20.4 },
  IsNormalSets: true,
  IsDeload: false,
  IsBackOffSet: false,
  BackOffSetWeight: { Lb: 0, Kg: 0 },
  IsMaxChallenge: false,
  IsLightSession: false,
  FirstWorkSetReps: 10,
  FirstWorkSetWeight: { Lb: 135, Kg: 61.23 },
  FirstWorkSet1RM: { Lb: 180, Kg: 81.65 },
  IsPyramid: false,
  IsReversePyramid: false,
  HistorySet: [],
  ReferenceSetHistory: {
    RepetitionNumber: 10,
    Weight: { Lb: 135, Kg: 61.23 },
    IsWarmups: false,
    Id: '1',
    ExerciseId: 1,
    IsBodyweight: false,
    IsUnilateral: false,
    ProgramLabel: 'Test Program',
    WorkoutId: '1',
    ExerciseLabel: 'Bench Press',
    LocalDate: new Date(),
    SessionDate: new Date(),
    IsTimeBased: false,
    Seconds: 0,
    TimeSpan: '',
    State: 'Valid',
    RIR: 2,
    IsBackOffSet: false,
    IsFinished: true,
  },
  MinReps: 8,
  MaxReps: 12,
  isPlateAvailable: true,
  isDumbbellAvailable: true,
  isPulleyAvailable: false,
  isBandsAvailable: false,
  Speed: 2,
  IsManual: false,
  ReferenseReps: 10,
  ReferenseWeight: { Lb: 135, Kg: 61.23 },
  IsDropSet: false,
}

// Mock exercise model
const mockExercise: ExerciseModel = {
  Id: 1,
  Label: 'Bench Press',
  IsSystemExercise: true,
  IsSwapTarget: false,
  IsFinished: false,
  IsUnilateral: false,
  IsTimeBased: false,
  IsEasy: false,
  IsMedium: true,
  IsBodyweight: false,
  VideoUrl: '',
  IsNextExercise: false,
  IsPlate: true,
  IsWeighted: false,
  IsPyramid: false,
  IsNormalSets: true,
  LocalVideo: '',
  IsAssisted: false,
}

describe('useOneRMTracking', () => {
  it('should calculate 1RM when weight and reps change', () => {
    const { result } = renderHook(() =>
      useOneRMTracking({
        weight: 100,
        reps: 10,
        exercise: mockExercise,
        recommendation: mockRecommendation,
        unit: 'kg',
        userBodyWeight: 80,
      })
    )

    expect(result.current.currentOneRM).toBe(133.3) // (0.0333 * 10) * 100 + 100
  })

  it('should calculate progress percentage based on last 1RM', () => {
    const { result } = renderHook(() =>
      useOneRMTracking({
        weight: 100,
        reps: 10,
        exercise: mockExercise,
        recommendation: mockRecommendation,
        unit: 'kg',
        userBodyWeight: 80,
      })
    )

    // Last 1RM from recommendation is 81.65 kg
    // Current 1RM is 133.3 kg
    // Progress = ((133.3 - 81.65) * 100) / 81.65 = 63.26%
    expect(result.current.oneRMProgress).toBe(63.26)
  })

  it('should handle weight changes and recalculate 1RM', () => {
    const onWeightChange = vi.fn()
    const { result, rerender } = renderHook(
      ({ weight, reps }) =>
        useOneRMTracking({
          weight,
          reps,
          exercise: mockExercise,
          recommendation: mockRecommendation,
          unit: 'kg',
          userBodyWeight: 80,
          onWeightChange,
        }),
      { initialProps: { weight: 100, reps: 10 } }
    )

    // Initial 1RM
    expect(result.current.currentOneRM).toBe(133.3)

    // Update weight
    act(() => {
      result.current.handleWeightChange('110')
    })

    expect(onWeightChange).toHaveBeenCalledWith(110)

    // Re-render with new weight
    rerender({ weight: 110, reps: 10 })
    expect(result.current.currentOneRM).toBe(146.63) // (0.0333 * 10) * 110 + 110
  })

  it('should handle reps changes and recalculate 1RM', () => {
    const onRepsChange = vi.fn()
    const { result, rerender } = renderHook(
      ({ weight, reps }) =>
        useOneRMTracking({
          weight,
          reps,
          exercise: mockExercise,
          recommendation: mockRecommendation,
          unit: 'kg',
          userBodyWeight: 80,
          onRepsChange,
        }),
      { initialProps: { weight: 100, reps: 10 } }
    )

    // Update reps
    act(() => {
      result.current.handleRepsChange('12')
    })

    expect(onRepsChange).toHaveBeenCalledWith(12)

    // Re-render with new reps
    rerender({ weight: 100, reps: 12 })
    expect(result.current.currentOneRM).toBe(139.96) // (0.0333 * 12) * 100 + 100
  })

  it('should handle bodyweight exercises with additional weight', () => {
    const bodyweightExercise = { ...mockExercise, IsBodyweight: true }
    const { result } = renderHook(() =>
      useOneRMTracking({
        weight: 20, // Additional weight
        reps: 10,
        exercise: bodyweightExercise,
        recommendation: mockRecommendation,
        unit: 'kg',
        userBodyWeight: 80,
      })
    )

    // Total weight = bodyweight + additional = 80 + 20 = 100
    expect(result.current.currentOneRM).toBe(133.3)
  })

  it('should handle weighted exercises (with weight vest)', () => {
    const weightedExercise = { ...mockExercise, IsWeighted: true }
    const { result } = renderHook(() =>
      useOneRMTracking({
        weight: 20, // Additional weight (vest)
        reps: 10,
        exercise: weightedExercise,
        recommendation: mockRecommendation,
        unit: 'kg',
        userBodyWeight: 80,
      })
    )

    // Total weight = bodyweight + additional = 80 + 20 = 100
    expect(result.current.currentOneRM).toBe(133.3)
  })

  it('should handle assisted exercises (subtract from bodyweight)', () => {
    const assistedExercise = { ...mockExercise, IsAssisted: true }
    const { result } = renderHook(() =>
      useOneRMTracking({
        weight: 20, // Assistance weight
        reps: 10,
        exercise: assistedExercise,
        recommendation: mockRecommendation,
        unit: 'kg',
        userBodyWeight: 80,
      })
    )

    // Effective weight = bodyweight - assistance = 80 - 20 = 60
    expect(result.current.currentOneRM).toBe(79.98) // (0.0333 * 10) * 60 + 60
  })

  it('should format progress message with positive percentage', () => {
    const { result } = renderHook(() =>
      useOneRMTracking({
        weight: 100,
        reps: 10,
        exercise: mockExercise,
        recommendation: mockRecommendation,
        unit: 'kg',
        userBodyWeight: 80,
      })
    )

    expect(result.current.progressMessage).toBe('+63.26%')
  })

  it('should format progress message with negative percentage', () => {
    const { result } = renderHook(() =>
      useOneRMTracking({
        weight: 50,
        reps: 10,
        exercise: mockExercise,
        recommendation: mockRecommendation,
        unit: 'kg',
        userBodyWeight: 80,
      })
    )

    // Current 1RM = 66.65, Last 1RM = 81.65
    // Progress = -18.37%
    expect(result.current.progressMessage).toBe('-18.37%')
  })

  it('should show last time info', () => {
    const { result } = renderHook(() =>
      useOneRMTracking({
        weight: 100,
        reps: 10,
        exercise: mockExercise,
        recommendation: mockRecommendation,
        unit: 'kg',
        userBodyWeight: 80,
      })
    )

    expect(result.current.lastTimeInfo).toBe('Last time: 10 x 61.23 kg')
  })

  it('should handle unit conversion for display', () => {
    const { result } = renderHook(() =>
      useOneRMTracking({
        weight: 135,
        reps: 10,
        exercise: mockExercise,
        recommendation: mockRecommendation,
        unit: 'lbs',
        userBodyWeight: 176, // 80kg in lbs
      })
    )

    expect(result.current.lastTimeInfo).toBe('Last time: 10 x 135 lbs')
  })

  it('should handle invalid weight input', () => {
    const onWeightChange = vi.fn()
    const { result } = renderHook(() =>
      useOneRMTracking({
        weight: 100,
        reps: 10,
        exercise: mockExercise,
        recommendation: mockRecommendation,
        unit: 'kg',
        userBodyWeight: 80,
        onWeightChange,
      })
    )

    act(() => {
      result.current.handleWeightChange('invalid')
    })

    expect(onWeightChange).not.toHaveBeenCalled()
  })

  it('should handle invalid reps input', () => {
    const onRepsChange = vi.fn()
    const { result } = renderHook(() =>
      useOneRMTracking({
        weight: 100,
        reps: 10,
        exercise: mockExercise,
        recommendation: mockRecommendation,
        unit: 'kg',
        userBodyWeight: 80,
        onRepsChange,
      })
    )

    act(() => {
      result.current.handleRepsChange('invalid')
    })

    expect(onRepsChange).not.toHaveBeenCalled()
  })

  it('should handle missing recommendation data', () => {
    const { result } = renderHook(() =>
      useOneRMTracking({
        weight: 100,
        reps: 10,
        exercise: mockExercise,
        recommendation: null,
        unit: 'kg',
        userBodyWeight: 80,
      })
    )

    expect(result.current.currentOneRM).toBe(133.3)
    expect(result.current.oneRMProgress).toBe(0)
    expect(result.current.progressMessage).toBe('')
    expect(result.current.lastTimeInfo).toBe('')
  })

  it('should handle missing exercise data', () => {
    const { result } = renderHook(() =>
      useOneRMTracking({
        weight: 100,
        reps: 10,
        exercise: null,
        recommendation: mockRecommendation,
        unit: 'kg',
        userBodyWeight: 80,
      })
    )

    expect(result.current.currentOneRM).toBe(133.3)
    expect(result.current.oneRMProgress).toBe(63.26)
  })

  it('should calculate 1RM for all sets, not just first work set', () => {
    const { result } = renderHook(() =>
      useOneRMTracking({
        weight: 100,
        reps: 10,
        exercise: mockExercise,
        recommendation: mockRecommendation,
        unit: 'kg',
        userBodyWeight: 80,
        isFirstWorkSet: false,
      })
    )

    // Should calculate progress for all sets
    expect(result.current.currentOneRM).toBe(133.3)
    expect(result.current.oneRMProgress).toBe(63.26)
    expect(result.current.progressMessage).toBe('+63.26%')
  })
})
