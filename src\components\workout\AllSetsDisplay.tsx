'use client'

import React from 'react'
import type { RecommendationModel, WorkoutLogSerieModel } from '@/types'
import { SetDisplay, type WorkoutSet } from './SetDisplay'
import { getSetType, getSetTypeTitle } from '@/utils/setTypeUtils'
import { HistoricalSetsDisplay } from './HistoricalSetsDisplay'

interface AllSetsDisplayProps {
  recommendation: RecommendationModel
  currentSetIndex: number
  completedSets: WorkoutLogSerieModel[]
  onSetClick: (setIndex: number) => void
  unit?: 'kg' | 'lbs'
}

export function AllSetsDisplay({
  recommendation,
  currentSetIndex,
  completedSets,
  onSetClick,
  unit = 'lbs',
}: AllSetsDisplayProps) {
  const totalSets = recommendation.Series || 0
  const warmupCount = recommendation.WarmupsCount || 0

  // Early return if no sets
  if (totalSets === 0) {
    return (
      <div className="px-4 py-6 text-center">
        <p className="text-gray-500">No sets available for this exercise</p>
      </div>
    )
  }

  // Get set type from recommendation
  const setType = getSetType(recommendation)
  const setTitle = getSetTypeTitle(setType)

  // Generate all sets (warmups + work sets)
  const allSets: WorkoutSet[] = []

  // Add warmup sets
  for (let i = 0; i < warmupCount; i++) {
    const warmupSet = recommendation.WarmUpsList?.[i]
    allSets.push({
      id: `warmup-${i}`,
      setNumber: i + 1,
      reps: warmupSet?.WarmUpReps || recommendation.WarmUpReps1 || 5,
      weight: warmupSet?.WarmUpWeightSet ||
        recommendation.WarmUpWeightSet1 || { Kg: 0, Lb: 0 },
      isWarmup: true,
      isFinished: false,
      isActive: false,
      unit,
    })
  }

  // Add work sets
  for (let i = 0; i < totalSets; i++) {
    const reps =
      setType === 'Rest-pause'
        ? recommendation.NbRepsPauses
        : recommendation.Reps

    allSets.push({
      id: `set-${i}`,
      setNumber: i + 1,
      reps,
      weight: recommendation.Weight,
      isWarmup: false,
      isFinished: false,
      isActive: false,
      unit,
      setTitle,
      restTime: setType === 'Rest-pause' ? recommendation.RpRest : undefined,
      nbPause: setType === 'Rest-pause' ? recommendation.NbPauses : undefined,
    })
  }

  return (
    <div className="space-y-4">
      {/* Historical Sets Display */}
      {recommendation.HistorySet && recommendation.HistorySet.length > 0 && (
        <div className="px-4">
          <HistoricalSetsDisplay
            historicalSets={recommendation.HistorySet}
            unit={unit}
          />
        </div>
      )}

      {/* All Sets Section */}
      <div className="bg-bg-primary">
        <div className="px-4 py-3 bg-bg-secondary border-b border-border-primary">
          <h3 className="text-base font-semibold text-text-primary">
            Today's Sets ({allSets.length})
          </h3>
          <p className="text-xs text-text-secondary mt-0.5">
            {warmupCount > 0 &&
              `${warmupCount} warm-up${warmupCount > 1 ? 's' : ''}, `}
            {totalSets} work set{totalSets !== 1 ? 's' : ''}
          </p>
        </div>

        <div className="px-4 py-3 space-y-2">
          {/* Warmup Sets */}
          {warmupCount > 0 && (
            <>
              <div className="flex items-center gap-2 mb-2">
                <div className="h-px flex-1 bg-border-secondary" />
                <span className="text-xs text-text-secondary font-medium px-2">
                  WARM-UP SETS
                </span>
                <div className="h-px flex-1 bg-border-secondary" />
              </div>
              {allSets.slice(0, warmupCount).map((set, index) => {
                const setIndex = index
                const isCompleted = completedSets.some(
                  (completedSet) =>
                    parseInt(completedSet.SetNo || '0') === setIndex + 1
                )
                const isCurrent = currentSetIndex === setIndex
                const completedSet = completedSets.find(
                  (completedSet) =>
                    parseInt(completedSet.SetNo || '0') === setIndex + 1
                )

                const displaySet: WorkoutSet = {
                  ...set,
                  isActive: isCurrent,
                  isFinished: isCompleted,
                  reps: completedSet ? completedSet.Reps : set.reps,
                  weight: completedSet ? completedSet.Weight : set.weight,
                }

                return (
                  <SetDisplay
                    key={set.id}
                    set={displaySet}
                    onClick={() => onSetClick(setIndex)}
                  />
                )
              })}
            </>
          )}

          {/* Work Sets */}
          {totalSets > 0 && (
            <>
              <div className="flex items-center gap-2 my-3">
                <div className="h-px flex-1 bg-border-secondary" />
                <span className="text-xs text-text-secondary font-medium px-2">
                  WORK SETS{setTitle && ` - ${setTitle.toUpperCase()}`}
                </span>
                <div className="h-px flex-1 bg-border-secondary" />
              </div>
              {allSets.slice(warmupCount).map((set, index) => {
                const setIndex = warmupCount + index
                const isCompleted = completedSets.some(
                  (completedSet) =>
                    parseInt(completedSet.SetNo || '0') === setIndex + 1
                )
                const isCurrent = currentSetIndex === setIndex
                const completedSet = completedSets.find(
                  (completedSet) =>
                    parseInt(completedSet.SetNo || '0') === setIndex + 1
                )

                const displaySet: WorkoutSet = {
                  ...set,
                  isActive: isCurrent,
                  isFinished: isCompleted,
                  reps: completedSet ? completedSet.Reps : set.reps,
                  weight: completedSet ? completedSet.Weight : set.weight,
                }

                return (
                  <SetDisplay
                    key={set.id}
                    set={displaySet}
                    onClick={() => onSetClick(setIndex)}
                  />
                )
              })}
            </>
          )}
        </div>

        {/* Progress Summary */}
        <div className="px-4 py-3 border-t border-border-primary bg-bg-secondary">
          <div className="flex items-center justify-between">
            <span className="text-sm text-text-secondary">
              Progress: {completedSets.length} of {allSets.length} completed
            </span>
            <div className="flex items-center gap-2">
              <div className="h-2 w-20 bg-bg-tertiary rounded-full overflow-hidden">
                <div
                  className="h-full bg-primary transition-all duration-300"
                  style={{
                    width: `${(completedSets.length / allSets.length) * 100}%`,
                  }}
                />
              </div>
              <span className="text-xs font-medium text-text-primary">
                {Math.round((completedSets.length / allSets.length) * 100)}%
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
