/**
 * One Rep Max Calculator
 *
 * Implements the same 1RM calculation logic as the mobile app
 * Formula: (0.0333 * reps) * weight + weight
 */

// Mobile app constant: AppThemeConstants.Coeficent = 0.0333
const ONE_RM_COEFFICIENT = 0.0333

/**
 * Calculate One Rep Max using the Dr. Muscle formula
 * @param weight - Weight in any unit (kg or lbs)
 * @param reps - Number of reps performed
 * @returns Calculated 1RM in the same unit as weight
 */
export function computeOneRM(weight: number, reps: number): number {
  // Handle edge cases
  if (reps <= 0) {
    return weight
  }

  // For single rep, 1RM equals the weight
  if (reps === 1) {
    return weight
  }

  // Formula from mobile app: (0.0333 * reps) * weight + weight
  return ONE_RM_COEFFICIENT * reps * weight + weight
}

/**
 * Calculate progress percentage between two 1RM values
 * @param lastOneRM - Previous 1RM value
 * @param currentOneRM - Current 1RM value
 * @returns Percentage change rounded to 2 decimal places
 */
export function calculateOneRMProgress(
  lastOneRM: number,
  currentOneRM: number
): number {
  // Handle edge cases
  if (lastOneRM <= 0) {
    return 0
  }

  // Calculate percentage change
  const progress = ((currentOneRM - lastOneRM) * 100) / lastOneRM

  // Round to 2 decimal places
  return Math.round(progress * 100) / 100
}

/**
 * Update 1RM calculation for a set
 * Matches MAUI UpdateOneRM method
 */
export function updateOneRM(
  weight: number,
  reps: number,
  exercise: any,
  recommendation: any,
  isKg: boolean,
  userBodyWeight: number,
  isFirstWorkSet: boolean
): {
  currentOneRM: number
  lastOneRM: number
  progress: number
  progressText: string
  lastTimeText: string
} {
  let effectiveWeight = weight

  // Handle bodyweight exercises
  if (exercise?.IsBodyweight || exercise?.IsWeighted) {
    effectiveWeight = weight + userBodyWeight
  }

  // Handle assisted exercises (subtract from bodyweight)
  if (exercise?.IsAssisted) {
    effectiveWeight = userBodyWeight - weight
  }

  // Calculate current 1RM
  const currentOneRM = computeOneRM(effectiveWeight, reps)

  // Calculate last 1RM from recommendation
  let lastOneRM = 0
  if (recommendation?.FirstWorkSetWeight && recommendation?.FirstWorkSetReps) {
    let lastWeight = isKg ? recommendation.FirstWorkSetWeight.Kg : recommendation.FirstWorkSetWeight.Lb

    if (exercise?.IsBodyweight || exercise?.IsWeighted) {
      lastWeight += userBodyWeight
    }

    if (exercise?.IsAssisted) {
      lastWeight = userBodyWeight - lastWeight
    }

    lastOneRM = computeOneRM(lastWeight, recommendation.FirstWorkSetReps)
  }

  // Calculate progress percentage
  const progress = calculateOneRMProgress(lastOneRM, currentOneRM)

  // Format progress text
  const progressText = progress >= 0 ? `+${progress.toFixed(2)}%` : `${progress.toFixed(2)}%`

  // Format last time text
  const lastTimeText = recommendation?.FirstWorkSetReps && recommendation?.FirstWorkSetWeight
    ? `Last time: ${recommendation.FirstWorkSetReps} x ${isKg
        ? `${Math.round(recommendation.FirstWorkSetWeight.Kg * 100) / 100} kg`
        : `${Math.round(recommendation.FirstWorkSetWeight.Lb * 100) / 100} lbs`}`
    : ''

  return {
    currentOneRM: Math.round(currentOneRM * 100) / 100,
    lastOneRM: Math.round(lastOneRM * 100) / 100,
    progress,
    progressText,
    lastTimeText
  }
}

/**
 * Truncate decimal to specified places (matches mobile app behavior)
 * @param value - Value to truncate
 * @param decimals - Number of decimal places
 * @returns Truncated value
 */
export function truncateDecimal(value: number, decimals: number): number {
  const factor = Math.pow(10, decimals)
  return Math.trunc(value * factor) / factor
}
