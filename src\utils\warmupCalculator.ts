import type { RecommendationModel, ExerciseModel, WarmUps, MultiUnityWeight } from '@/types'

export interface WarmupSet extends WarmUps {
  warmUpWeightSet: {
    Lb: number
    Kg: number
  }
  warmUpReps: number
}

export interface WarmupCalculationConfig {
  warmupsCount: number
  workingWeight: MultiUnityWeight
  workingReps: number
  incrementValue: number
  minWeight?: number
  maxWeight?: number
  isPlateAvailable: boolean
  isBodyweight: boolean
  barbellWeight: number
  availablePlates: string
  userBodyWeight: number
  isKg: boolean
}

interface UserSettings {
  barbellWeight: number
  unit: 'lbs' | 'kg'
  bodyWeight: number
  availablePlates: string
}

function getUserSettings(): UserSettings {
  // TODO: Get user settings from proper source when available
  // For now, use default values
  return {
    barbellWeight: 45, // Default barbell weight in lbs
    unit: 'lbs',
    bodyWeight: 180, // Default body weight in lbs
    availablePlates: 'all',
  }
}

/**
 * WarmupCalculator class implementing the exact MAUI app warmup algorithm
 */
export class WarmupCalculator {
  private static readonly KG_TO_LB = 2.20462
  private static readonly LB_TO_KG = 0.453592
  private static readonly INITIAL_WEIGHT_PERCENTAGE = 0.5
  private static readonly FINAL_WEIGHT_PERCENTAGE = 0.85
  private static readonly BODYWEIGHT_INITIAL_REPS_PERCENTAGE = 0.6
  private static readonly WEIGHTED_INITIAL_REPS_PERCENTAGE = 0.75
  private static readonly WEIGHTED_FINAL_REPS_PERCENTAGE = 0.4
  private static readonly MIN_REPS_THRESHOLD = 5.01
  private static readonly MIN_REPS_DEFAULT = 6
  private static readonly MIN_REPS_WEIGHTED = 3

  static computeWarmups(config: WarmupCalculationConfig): WarmupSet[] {
    // No warmups if count is 0
    if (config.warmupsCount === 0) {
      return []
    }

    // Handle missing weight data
    if (
      !config.workingWeight ||
      (config.workingWeight.Kg === 0 && !config.isBodyweight)
    ) {
      return []
    }

    const warmups: WarmupSet[] = []
    const warmupsCount = config.warmupsCount
    const newWarmupCount = warmupsCount > 1 ? warmupsCount - 1 : warmupsCount

    // Calculate weight progression (using kg for calculations)
    const weightProgression = this.calculateWeightProgression(
      config.workingWeight.Kg,
      config.warmupsCount,
      newWarmupCount
    )

    // Calculate reps progression
    const repsProgression = this.calculateRepsProgression(
      config.workingReps,
      config.warmupsCount,
      newWarmupCount,
      config.isBodyweight,
      config.isPlateAvailable
    )

    // Generate warmup sets
    for (let i = 0; i < warmupsCount; i++) {
      let warmupWeight: number
      let warmupReps: number

      if (config.isBodyweight) {
        // Bodyweight exercises use working weight with varying reps
        warmupWeight = config.workingWeight.Kg
        warmupReps = Math.ceil(repsProgression.initial + (repsProgression.increment * i))
      } else {
        // Weighted exercises progress weight from 50% to 85%
        warmupWeight = weightProgression.initial +
          (weightProgression.increment * i)
        warmupReps = Math.ceil(repsProgression.initial - (repsProgression.increment * i))

        // Apply minimum reps for weighted exercises
        if (warmupReps < this.MIN_REPS_WEIGHTED && !config.isPlateAvailable) {
          warmupReps = this.MIN_REPS_WEIGHTED
        }
      }

      // Round to available increments
      warmupWeight = this.roundToNearestIncrement(
        warmupWeight,
        config.incrementValue,
        config.minWeight,
        config.maxWeight
      )

      // Apply plate calculations for barbell exercises
      if (config.isPlateAvailable && !config.isBodyweight) {
        warmupWeight = this.calculatePlatesWeight(
          config.availablePlates,
          warmupWeight,
          config.barbellWeight,
          config.isKg
        )
      }

      warmups.push({
        warmUpWeightSet: {
          Kg: warmupWeight,
          Lb: warmupWeight * this.KG_TO_LB
        },
        warmUpReps: warmupReps,
        WarmUpReps: warmupReps,
        WarmUpWeightSet: {
          Kg: warmupWeight,
          Lb: warmupWeight * this.KG_TO_LB
        },
      })
    }

    // Special case: First warmup for weighted exercises is bodyweight only
    if (!config.isBodyweight && warmups.length > 0) {
      warmups[0].warmUpWeightSet = { Kg: 0, Lb: 0 }
      warmups[0].WarmUpWeightSet = { Kg: 0, Lb: 0 }
    }

    return warmups
  }

  private static calculateWeightProgression(
    workingWeightKg: number,
    warmupsCount: number,
    newWarmupCount: number
  ) {
    const initialWeight = workingWeightKg * this.INITIAL_WEIGHT_PERCENTAGE
    const finalWeight = workingWeightKg * this.FINAL_WEIGHT_PERCENTAGE
    const weightIncrement = (finalWeight - initialWeight) / newWarmupCount

    return {
      initial: initialWeight,
      increment: weightIncrement
    }
  }

  private static calculateRepsProgression(
    workingReps: number,
    warmupsCount: number,
    newWarmupCount: number,
    isBodyweight: boolean,
    isPlateAvailable: boolean
  ) {
    let initialReps: number
    let repsIncrement: number

    if (isBodyweight) {
      // Bodyweight: Start at 40% and go up to 60%
      initialReps = workingReps * 0.4
      repsIncrement = warmupsCount === 1
        ? 0
        : (workingReps * 0.6 - workingReps * 0.4) / newWarmupCount
    } else {
      // Weighted: Start at 75% and go down to 40%
      initialReps = workingReps * this.WEIGHTED_INITIAL_REPS_PERCENTAGE
      repsIncrement = (workingReps * this.WEIGHTED_INITIAL_REPS_PERCENTAGE - workingReps * this.WEIGHTED_FINAL_REPS_PERCENTAGE) / newWarmupCount

      // Minimum reps adjustment for low rep ranges
      if (initialReps < this.MIN_REPS_THRESHOLD && !isPlateAvailable) {
        initialReps = this.MIN_REPS_DEFAULT
      }
    }

    return {
      initial: initialReps,
      increment: repsIncrement
    }
  }

  private static roundToNearestIncrement(
    weight: number,
    increment: number,
    min?: number,
    max?: number
  ): number {
    const rounded = Math.round(weight / increment) * increment

    if (min && rounded < min) return min
    if (max && rounded > max) return max

    return rounded
  }

  private static calculatePlatesWeight(
    availablePlates: string,
    targetWeight: number,
    barbellWeight: number,
    isKg: boolean
  ): number {
    // Return barbell weight if target is less than barbell
    if (targetWeight <= barbellWeight) {
      return barbellWeight
    }

    // Calculate weight needed per side
    const weightPerSide = (targetWeight - barbellWeight) / 2

    // Available plate weights (sorted descending)
    const plates = isKg
      ? [20, 15, 10, 5, 2.5, 1.25, 0.5, 0.25]
      : [45, 35, 25, 10, 5, 2.5]

    // Parse available plates from string (if needed)
    // For now, use all plates. Could be enhanced to parse from availablePlates string

    let remainingWeight = weightPerSide
    let totalPlateWeight = 0

    // Greedy algorithm to select plates
    for (const plate of plates) {
      while (remainingWeight >= plate) {
        totalPlateWeight += plate
        remainingWeight -= plate
      }
    }

    // Return barbell + plates on both sides
    return barbellWeight + (totalPlateWeight * 2)
  }
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use WarmupCalculator.computeWarmups instead
 */
export function computeWarmups(
  recommendation: RecommendationModel,
  exercise: ExerciseModel
): WarmupSet[] {
  if (recommendation.WarmupsCount === 0) {
    return []
  }

  // Handle missing weight data
  if (
    !recommendation.Weight ||
    (recommendation.Weight.Lb === 0 && !exercise.IsBodyweight)
  ) {
    return []
  }

  const userSettings = getUserSettings()
  const warmups: WarmupSet[] = []
  const warmupsCount = recommendation.WarmupsCount

  // Calculate warmup progression (original algorithm)
  const workingWeight = recommendation.Weight
  const initialWeight = workingWeight.Lb * 0.5
  const newWarmupCount = warmupsCount > 1 ? warmupsCount - 1 : warmupsCount
  const weightIncrement =
    (workingWeight.Lb * 0.85 - workingWeight.Lb * 0.5) / newWarmupCount

  // Calculate reps progression (original algorithm)
  let initialReps: number
  let repsIncrement: number

  if (exercise.IsBodyweight) {
    initialReps = recommendation.Reps * 0.4
    repsIncrement =
      warmupsCount === 1
        ? 0
        : (recommendation.Reps * 0.6 - recommendation.Reps * 0.4) /
          newWarmupCount
  } else {
    initialReps = recommendation.Reps * 0.75
    repsIncrement =
      (recommendation.Reps * 0.75 - recommendation.Reps * 0.4) / newWarmupCount

    if (initialReps < 5.01 && !recommendation.isPlateAvailable) {
      initialReps = 6
    }
  }

  // Generate warmup sets (original algorithm)
  for (let i = 0; i < warmupsCount; i++) {
    let warmupWeight: number
    let warmupReps: number

    if (exercise.IsBodyweight) {
      warmupWeight = 0
      warmupReps = Math.ceil(initialReps + repsIncrement * i)
    } else {
      warmupWeight = initialWeight + weightIncrement * i
      warmupReps = Math.ceil(initialReps - repsIncrement * i)

      if (warmupReps < 3 && !recommendation.isPlateAvailable) {
        warmupReps = 3
      }
    }

    // Apply equipment-specific calculations (original algorithm)
    if (
      recommendation.isPlateAvailable &&
      !exercise.IsBodyweight &&
      warmupWeight > 0
    ) {
      warmupWeight = calculatePlatesWeight(
        warmupWeight,
        userSettings.barbellWeight,
        userSettings.unit === 'kg'
      )
    }

    warmups.push({
      warmUpWeightSet: {
        Lb: warmupWeight,
        Kg: warmupWeight * 0.453592,
      },
      warmUpReps: warmupReps,
      WarmUpReps: warmupReps,
      WarmUpWeightSet: {
        Lb: warmupWeight,
        Kg: warmupWeight * 0.453592,
      },
    })
  }

  // First warmup for weighted exercises is bodyweight only (original algorithm)
  if (exercise.IsWeighted && warmups.length > 0 && !exercise.IsBodyweight) {
    const firstWarmup = warmups[0]
    if (firstWarmup) {
      warmups[0] = {
        warmUpWeightSet: { Lb: 0, Kg: 0 },
        warmUpReps: firstWarmup.warmUpReps,
        WarmUpReps: firstWarmup.WarmUpReps,
        WarmUpWeightSet: { Lb: 0, Kg: 0 },
      }
    }
  }

  return warmups
}

function calculatePlatesWeight(
  targetWeight: number,
  barbellWeight: number,
  isKg: boolean
): number {
  // Round to nearest plate increment (5 lbs or 2.5 kg)
  const increment = isKg ? 2.5 : 5
  const plateWeight = targetWeight - barbellWeight
  const roundedPlateWeight = Math.round(plateWeight / increment) * increment
  return Math.max(barbellWeight, barbellWeight + roundedPlateWeight)
}
