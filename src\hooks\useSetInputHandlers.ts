import { useState, useCallback } from 'react'
import { useSetInputValidation, ValidationError } from './useSetInputValidation'
import { roundToNearestIncrement, truncateDecimal } from '@/utils/weightUtils'

interface SetInputHandlersProps {
  reps: number | undefined
  weight: number
  duration: number
  unit: 'lbs' | 'kg'
  isBodyweight?: boolean
  isTimeBased?: boolean
  onChange: (data: {
    reps?: number
    weight?: number
    duration?: number
  }) => void
}

export function useSetInputHandlers({
  reps,
  weight,
  duration,
  unit,
  isBodyweight = false,
  isTimeBased = false,
  onChange,
}: SetInputHandlersProps) {
  const [errors, setErrors] = useState<ValidationError>({})
  const { validateReps, validateWeight, validateDuration } =
    useSetInputValidation({
      isBodyweight,
    })

  const handleRepsChange = useCallback(
    (value: number | string) => {
      const numValue = typeof value === 'string' ? parseInt(value, 10) : value
      if (Number.isNaN(numValue)) return

      const error = validateReps(numValue)
      setErrors((prev) => ({ ...prev, reps: error }))
      onChange({ reps: numValue, weight })
    },
    [onChange, validateReps, weight]
  )

  const handleWeightChange = useCallback(
    (value: number | string) => {
      const numValue = typeof value === 'string' ? parseFloat(value) : value
      if (Number.isNaN(numValue)) return

      // Truncate to 2 decimal places for display
      const truncatedValue = truncateDecimal(numValue, 2)

      const error = validateWeight(truncatedValue)
      setErrors((prev) => ({ ...prev, weight: error }))
      if (isTimeBased) {
        onChange({ weight: truncatedValue, duration })
      } else {
        onChange({ reps, weight: truncatedValue })
      }
    },
    [onChange, validateWeight, reps, isTimeBased, duration]
  )

  const handleDurationChange = useCallback(
    (value: number | string) => {
      const numValue = typeof value === 'string' ? parseInt(value, 10) : value
      if (Number.isNaN(numValue)) return

      const error = validateDuration(numValue)
      setErrors((prev) => ({ ...prev, duration: error }))
      onChange({ weight, duration: numValue })
    },
    [onChange, validateDuration, weight]
  )

  const incrementWeight = useCallback(() => {
    const increment = unit === 'kg' ? 2.5 : 5
    const newWeight = roundToNearestIncrement(
      weight + increment,
      increment,
      0,
      1000
    )
    handleWeightChange(newWeight)
  }, [weight, unit, handleWeightChange])

  const decrementWeight = useCallback(() => {
    const increment = unit === 'kg' ? 2.5 : 5
    const newWeight = roundToNearestIncrement(
      weight - increment,
      increment,
      0,
      1000
    )
    handleWeightChange(newWeight)
  }, [weight, unit, handleWeightChange])

  const incrementReps = useCallback(() => {
    if (isTimeBased) return
    const currentReps = reps ?? 0
    const newReps = Math.min(currentReps + 1, 100)
    handleRepsChange(newReps)
  }, [reps, isTimeBased, handleRepsChange])

  const decrementReps = useCallback(() => {
    if (isTimeBased) return
    const currentReps = reps ?? 1
    const newReps = Math.max(currentReps - 1, 1)
    handleRepsChange(newReps)
  }, [reps, isTimeBased, handleRepsChange])

  return {
    errors,
    handleRepsChange,
    handleWeightChange,
    handleDurationChange,
    incrementWeight,
    decrementWeight,
    incrementReps,
    decrementReps,
  }
}
