import { Page } from '@playwright/test'

export async function mockLoginAPI(page: Page) {
  // Mock login endpoint
  await page.route('**/api/Account/Login*', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        Token: 'mock-jwt-token',
        RefreshToken: 'mock-refresh-token',
        User: {
          Email: '<EMAIL>',
          FirstName: 'Test',
          LastName: 'User',
        },
      }),
    })
  })

  // Mock auth check endpoint
  await page.route('**/api/auth/check', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        isAuthenticated: true,
        user: {
          id: 1,
          email: '<EMAIL>',
          name: 'Test User',
        },
      }),
    })
  })

  // Mock token refresh endpoint
  await page.route('**/api/auth/refresh', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        token: 'mock-auth-token-refreshed',
      }),
    })
  })
}
