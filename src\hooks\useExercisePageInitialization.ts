/**
 * Custom hook for exercise page initialization logic
 * Extracted from ExercisePageClient to reduce file size and improve maintainability
 */

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { debugLog } from '@/utils/debugLog'

export interface ExercisePageInitializationState {
  isInitializing: boolean
  loadingError: Error | null
  retryInitialization: () => Promise<void>
}

export function useExercisePageInitialization(
  exerciseId: number
): ExercisePageInitializationState {
  const router = useRouter()
  const [isInitializing, setIsInitializing] = useState(true)
  const [loadingError, setLoadingError] = useState<Error | null>(null)

  const {
    todaysWorkout,
    isLoadingWorkout,
    startWorkout,
    exercises,
    workoutSession,
    loadRecommendation,
    updateExerciseWorkSets,
  } = useWorkout()

  const {
    setCurrentExerciseById,
    loadingStates,
    getCachedExerciseRecommendation,
  } = useWorkoutStore()

  // Retry function for manual initialization
  const retryInitialization = async () => {
    try {
      setIsInitializing(true)
      setLoadingError(null)

      // Start workout if needed
      if (!workoutSession && todaysWorkout && !isLoadingWorkout) {
        const workoutGroup = todaysWorkout[0]
        const workout = workoutGroup?.WorkoutTemplates?.[0]

        if (workout) {
          await startWorkout(todaysWorkout)
        } else {
          router.replace('/workout')
          return
        }
      }

      // Set current exercise and load recommendations
      if (exerciseId) {
        setCurrentExerciseById(exerciseId)

        // Check if recommendation is loaded for this exercise
        const hasRecommendation = getCachedExerciseRecommendation(exerciseId)
        const isLoadingRecommendation = loadingStates.get(exerciseId)

        // If no recommendation and not loading, trigger loading
        if (!hasRecommendation && !isLoadingRecommendation) {
          const exercise = exercises?.find((ex) => ex.Id === exerciseId)
          if (exercise) {
            loadRecommendation(exerciseId, exercise.Label || 'Exercise')
          }
        }

        // Pre-load recommendation if not already loaded using alternative method
        const exercise = exercises?.find((ex) => ex.Id === exerciseId)
        if (exercise && (!exercise.sets || exercise.sets.length === 0)) {
          updateExerciseWorkSets(exerciseId, [])
        }
      }
    } catch (error) {
      debugLog.error('Failed to retry initialization:', error)
      setLoadingError(
        error instanceof Error
          ? error
          : new Error('Failed to retry initialization')
      )
    } finally {
      setIsInitializing(false)
    }
  }

  // Effect 1: Start workout if needed
  useEffect(() => {
    async function startWorkoutIfNeeded() {
      try {
        setLoadingError(null)

        if (!workoutSession && todaysWorkout && !isLoadingWorkout) {
          debugLog('🚀 [ExercisePageClient] Starting workout...', {
            todaysWorkout,
          })

          const workoutGroup = todaysWorkout[0]
          const workout = workoutGroup?.WorkoutTemplates?.[0]

          if (workout) {
            await startWorkout(todaysWorkout)
            debugLog('✅ [ExercisePageClient] Workout started successfully')
          } else {
            debugLog.error(
              '❌ [ExercisePageClient] No workout template found, redirecting...'
            )
            router.replace('/workout')
          }
        }
      } catch (error) {
        debugLog.error('Failed to start workout:', error)
        setLoadingError(
          error instanceof Error ? error : new Error('Failed to start workout')
        )
      }
    }

    startWorkoutIfNeeded()
  }, [workoutSession, todaysWorkout, isLoadingWorkout, startWorkout, router])

  // Effect 2: Set current exercise and load recommendations
  useEffect(() => {
    async function setupExercise() {
      try {
        if (!exerciseId) return

        debugLog('🎯 [ExercisePageClient] Setting up exercise', {
          exerciseId,
          hasExercises: !!exercises,
          exercisesCount: exercises?.length || 0,
        })

        // Set current exercise by ID
        setCurrentExerciseById(exerciseId)

        // Check if recommendation is loaded for this exercise
        const hasRecommendation = getCachedExerciseRecommendation(exerciseId)
        const isLoadingRecommendation = loadingStates.get(exerciseId)

        debugLog('💭 [ExercisePageClient] Recommendation status', {
          exerciseId,
          hasRecommendation: !!hasRecommendation,
          isLoadingRecommendation,
        })

        // If no recommendation and not loading, trigger loading
        if (!hasRecommendation && !isLoadingRecommendation) {
          const exercise = exercises?.find((ex) => ex.Id === exerciseId)
          debugLog('🔍 [ExercisePageClient] Found exercise', {
            exerciseId,
            exercise,
            exercises: exercises?.map((ex) => ({ id: ex.Id, label: ex.Label })),
          })

          if (exercise) {
            debugLog('📡 [ExercisePageClient] Loading recommendation...', {
              exerciseId,
              exerciseLabel: exercise.Label,
            })
            loadRecommendation(exerciseId, exercise.Label || 'Exercise')
          } else {
            debugLog.error(
              '❌ [ExercisePageClient] Exercise not found in exercises list',
              {
                exerciseId,
                availableIds: exercises?.map((ex) => ex.Id),
              }
            )
          }
        }

        // Pre-load recommendation if not already loaded using alternative method
        const exercise = exercises?.find((ex) => ex.Id === exerciseId)
        if (exercise && (!exercise.sets || exercise.sets.length === 0)) {
          debugLog(
            '🔧 [ExercisePageClient] Updating exercise work sets to empty array'
          )
          updateExerciseWorkSets(exerciseId, [])
        }
      } catch (error) {
        debugLog.error('Failed to setup exercise:', error)
        setLoadingError(
          error instanceof Error ? error : new Error('Failed to setup exercise')
        )
      } finally {
        setIsInitializing(false)
      }
    }

    setupExercise()
  }, [
    exerciseId,
    exercises,
    setCurrentExerciseById,
    getCachedExerciseRecommendation,
    loadingStates,
    loadRecommendation,
    updateExerciseWorkSets,
  ])

  return {
    isInitializing,
    loadingError,
    retryInitialization,
  }
}
