/**
 * Exercise type detection and helper utilities
 * Determines exercise characteristics for warmup calculations
 */

export interface ExerciseInfo {
  isBodyweight: boolean
  isWeighted: boolean
  usesPlates: boolean
  exerciseType: 'bodyweight' | 'barbell' | 'dumbbell' | 'machine' | 'cable'
}

export interface UserSettings {
  unit: 'kg' | 'lbs'
  barbellWeight?: number
  bodyWeight?: number
  availablePlates?: string
}

export class ExerciseHelpers {
  // Common bodyweight exercise patterns
  private static readonly BODYWEIGHT_PATTERNS = [
    'push-up', 'push up', 'pushup',
    'pull-up', 'pull up', 'pullup',
    'chin-up', 'chin up', 'chinup',
    'dip', 'plank', 'burpee',
    'squat (bodyweight)', 'bodyweight squat',
    'lunge (bodyweight)', 'bodyweight lunge',
    'crunch', 'sit-up', 'sit up',
    'mountain climber', 'jumping jack',
    'air squat', 'pistol squat',
    'handstand push-up', 'muscle-up'
  ]

  // Barbell exercise patterns (uses plates)
  private static readonly BARBELL_PATTERNS = [
    'barbell', 'bench press', 'squat', 'deadlift',
    'overhead press', 'military press', 'row',
    'curl', 'upright row', 'shrug',
    'hip thrust', 'good morning', 'lunge',
    'front squat', 'back squat', 'sumo deadlift',
    'romanian deadlift', 'bent over row',
    'pendlay row', 'clean', 'snatch',
    'thruster', 'push press'
  ]

  // Dumbbell exercise patterns
  private static readonly DUMBBELL_PATTERNS = [
    'dumbbell', 'db ', ' db',
    'dumbell', // common misspelling
  ]

  // Cable exercise patterns
  private static readonly CABLE_PATTERNS = [
    'cable', 'lat pulldown', 'cable row',
    'tricep pushdown', 'cable fly',
    'cable crossover', 'face pull'
  ]

  /**
   * Analyze exercise name to determine its characteristics
   */
  static getExerciseInfo(exerciseName: string): ExerciseInfo {
    const nameLower = exerciseName.toLowerCase()

    // Check if bodyweight first
    const isBodyweight = this.BODYWEIGHT_PATTERNS.some(pattern =>
      nameLower.includes(pattern)
    )

    // Determine exercise type with priority order
    let exerciseType: ExerciseInfo['exerciseType'] = 'machine'
    let usesPlates = false

    if (isBodyweight) {
      exerciseType = 'bodyweight'
    } else if (this.DUMBBELL_PATTERNS.some(pattern => nameLower.includes(pattern))) {
      exerciseType = 'dumbbell'
    } else if (this.CABLE_PATTERNS.some(pattern => nameLower.includes(pattern))) {
      exerciseType = 'cable'
    } else if (this.BARBELL_PATTERNS.some(pattern => nameLower.includes(pattern))) {
      exerciseType = 'barbell'
      usesPlates = true
    }

    return {
      isBodyweight,
      isWeighted: !isBodyweight,
      usesPlates,
      exerciseType
    }
  }

  /**
   * Get appropriate barbell weight based on exercise type and user settings
   */
  static getBarbellWeight(exerciseName: string, userSettings?: UserSettings): number {
    const nameLower = exerciseName.toLowerCase()
    const isKg = userSettings?.unit === 'kg'
    
    // Check user settings first
    if (userSettings?.barbellWeight) {
      return userSettings.barbellWeight
    }
    
    // Special cases for different barbells
    if (nameLower.includes('ez bar') || nameLower.includes('curl bar')) {
      return isKg ? 7 : 15
    }
    
    if (nameLower.includes('trap bar') || nameLower.includes('hex bar')) {
      return isKg ? 25 : 55
    }
    
    if (nameLower.includes('safety squat bar') || nameLower.includes('ssb')) {
      return isKg ? 32 : 70
    }
    
    // Standard Olympic barbell
    return isKg ? 20 : 45
  }

  /**
   * Determine if exercise should use plate calculations
   */
  static shouldUsePlateCalculations(exerciseName: string, exerciseInfo?: ExerciseInfo): boolean {
    const info = exerciseInfo || this.getExerciseInfo(exerciseName)
    return info.usesPlates && info.exerciseType === 'barbell'
  }

  /**
   * Get default increment value based on exercise type
   */
  static getDefaultIncrement(exerciseName: string, isKg: boolean): number {
    const exerciseInfo = this.getExerciseInfo(exerciseName)
    
    if (exerciseInfo.isBodyweight) {
      return 1 // Reps increment
    }
    
    if (exerciseInfo.exerciseType === 'barbell') {
      return isKg ? 2.5 : 5 // Standard plate increments
    }
    
    if (exerciseInfo.exerciseType === 'dumbbell') {
      return isKg ? 1 : 2.5 // Smaller dumbbell increments
    }
    
    // Machine/cable default
    return isKg ? 1.25 : 2.5
  }

  /**
   * Get available plates string based on user settings
   */
  static getAvailablePlates(userSettings?: UserSettings): string {
    if (userSettings?.availablePlates) {
      return userSettings.availablePlates
    }
    
    // Default to all standard plates
    const isKg = userSettings?.unit === 'kg'
    return isKg 
      ? '20,15,10,5,2.5,1.25,0.5,0.25'
      : '45,35,25,10,5,2.5'
  }

  /**
   * Parse available plates string into array of numbers
   */
  static parseAvailablePlates(availablePlates: string): number[] {
    if (!availablePlates || availablePlates === 'all') {
      return [] // Will use default plates in calculator
    }
    
    return availablePlates
      .split(',')
      .map(plate => parseFloat(plate.trim()))
      .filter(plate => !isNaN(plate))
      .sort((a, b) => b - a) // Sort descending
  }

  /**
   * Estimate if user has access to plates based on exercise and settings
   */
  static estimatePlateAvailability(
    exerciseName: string, 
    userSettings?: UserSettings
  ): boolean {
    const exerciseInfo = this.getExerciseInfo(exerciseName)
    
    // Bodyweight exercises don't use plates
    if (exerciseInfo.isBodyweight) {
      return false
    }
    
    // Barbell exercises typically use plates
    if (exerciseInfo.exerciseType === 'barbell') {
      return true
    }
    
    // Other equipment types typically don't use plates
    return false
  }
}
