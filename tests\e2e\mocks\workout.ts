import { Page } from '@playwright/test'

interface MockOptions {
  recommendationStatus?: number
}

export async function mockWorkoutAPI(page: Page, options: MockOptions = {}) {
  // Mock workout template groups
  await page.route(
    '**/api/Workout/GetUserWorkoutTemplateGroup*',
    async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            Id: 1,
            Label: 'Test Workout',
            WorkoutTemplates: [
              {
                Id: 101,
                Label: 'Day 1',
                Exercices: [
                  {
                    Id: 1,
                    Label: 'Bench Press',
                    IsNextExercise: true,
                    IsFinished: false,
                  },
                  {
                    Id: 2,
                    Label: 'Squat',
                    IsNextExercise: false,
                    IsFinished: false,
                  },
                ],
              },
            ],
          },
        ]),
      })
    }
  )

  // Mock workout recommendations endpoint
  await page.route(
    '**/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew',
    async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          ExerciseId: 217,
          Series: '3',
          Reps: 8,
          Weight: {
            Kg: 60,
            Lb: 132.28,
          },
          WarmUpsList: [
            {
              Weight: 50.0,
              Reps: 5,
              IsWarmup: true,
              SetNo: '1',
            },
            {
              Weight: 55.0,
              Reps: 5,
              IsWarmup: true,
              SetNo: '2',
            },
          ],
          FirstWorkSetWeight: 60.0,
          RestTime: 180,
        }),
      })
    }
  )

  // Mock recommendations
  await page.route(
    '**/api/Recommendation/GetRecommendation*',
    async (route) => {
      if (options.recommendationStatus === 404) {
        await route.fulfill({
          status: 404,
          contentType: 'application/json',
          body: JSON.stringify({ message: 'No recommendation found' }),
        })
      } else {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            Reps: 10,
            Weight: { Lb: 100, Kg: 45.4 },
            RIR: 2,
          }),
        })
      }
    }
  )

  // Mock save set endpoint
  await page.route('**/api/Workout/SaveSet', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        success: true,
        setId: Date.now(),
      }),
    })
  })

  // Mock exercise sets
  await page.route('**/api/Exercise/GetUserWorkoutSets*', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify([]),
    })
  })

  // Mock complete workout endpoint
  await page.route('**/api/Workout/CompleteWorkout', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        success: true,
        workoutId: Date.now(),
      }),
    })
  })
}