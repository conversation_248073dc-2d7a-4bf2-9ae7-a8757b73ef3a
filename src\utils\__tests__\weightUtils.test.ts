import { describe, it, expect } from 'vitest'
import {
  formatWeight,
  calculatePercentageChange,
  roundWeight,
  roundToNearestIncrement,
  truncateDecimal,
} from '../weightUtils'
import type { MultiUnityWeight } from '@/types'

describe('weightUtils', () => {
  describe('formatWeight', () => {
    it('should format weight in pounds', () => {
      const weight: MultiUnityWeight = { Lb: 100, Kg: 45.36 }
      expect(formatWeight(weight, 'lbs')).toBe('100 lbs')
    })

    it('should format weight in kilograms', () => {
      const weight: MultiUnityWeight = { Lb: 100, Kg: 45.36 }
      expect(formatWeight(weight, 'kg')).toBe('45.36 kg')
    })

    it('should handle decimal values', () => {
      const weight: MultiUnityWeight = { Lb: 102.5, Kg: 46.5 }
      expect(formatWeight(weight, 'lbs')).toBe('102.5 lbs')
    })

    it('should handle null weight', () => {
      expect(formatWeight(null as any)).toBe('0 lbs')
    })

    it('should format with 0.## pattern (mobile format)', () => {
      const weight1: MultiUnityWeight = { Lb: 100, Kg: 45.36 }
      expect(formatWeight(weight1, 'lbs')).toBe('100 lbs')

      const weight2: MultiUnityWeight = { Lb: 100.5, Kg: 45.59 }
      expect(formatWeight(weight2, 'lbs')).toBe('100.5 lbs')

      const weight3: MultiUnityWeight = { Lb: 100.25, Kg: 45.47 }
      expect(formatWeight(weight3, 'lbs')).toBe('100.25 lbs')

      const weight4: MultiUnityWeight = { Lb: 100.999, Kg: 45.45 }
      expect(formatWeight(weight4, 'lbs')).toBe('101 lbs')
    })

    it('should format kilograms with 0.## pattern', () => {
      const weight1: MultiUnityWeight = { Lb: 220.46, Kg: 100 }
      expect(formatWeight(weight1, 'kg')).toBe('100 kg')

      const weight2: MultiUnityWeight = { Lb: 221.56, Kg: 100.5 }
      expect(formatWeight(weight2, 'kg')).toBe('100.5 kg')

      const weight3: MultiUnityWeight = { Lb: 221.12, Kg: 100.25 }
      expect(formatWeight(weight3, 'kg')).toBe('100.25 kg')

      const weight4: MultiUnityWeight = { Lb: 221.78, Kg: 100.599 }
      expect(formatWeight(weight4, 'kg')).toBe('100.6 kg')
    })

    it('should support isKg boolean parameter for backward compatibility', () => {
      const weight: MultiUnityWeight = { Lb: 100, Kg: 45.36 }
      expect(formatWeight(weight, true)).toBe('45.36 kg')
      expect(formatWeight(weight, false)).toBe('100 lbs')
    })
  })

  describe('calculatePercentageChange', () => {
    it('should calculate positive percentage change', () => {
      expect(calculatePercentageChange(110, 100)).toBe(10)
    })

    it('should calculate negative percentage change', () => {
      expect(calculatePercentageChange(90, 100)).toBe(-10)
    })

    it('should handle zero previous value', () => {
      expect(calculatePercentageChange(100, 0)).toBeNull()
    })

    it('should round to 1 decimal place', () => {
      expect(calculatePercentageChange(105, 100)).toBe(5)
      expect(calculatePercentageChange(105.5, 100)).toBe(5.5)
    })
  })

  describe('roundWeight', () => {
    it('should round floating-point precision errors', () => {
      expect(roundWeight(35.00000000000004)).toBe(35)
      expect(roundWeight(45.00000000000001)).toBe(45)
      expect(roundWeight(99.99999999999999)).toBe(100)
    })

    it('should preserve valid decimal values', () => {
      expect(roundWeight(35.5)).toBe(35.5)
      expect(roundWeight(45.25)).toBe(45.25)
      expect(roundWeight(102.75)).toBe(102.75)
    })

    it('should round to 2 decimal places', () => {
      expect(roundWeight(35.123456)).toBe(35.12)
      expect(roundWeight(45.999)).toBe(46)
      expect(roundWeight(102.005)).toBe(102.01)
    })

    it('should handle whole numbers', () => {
      expect(roundWeight(35)).toBe(35)
      expect(roundWeight(100)).toBe(100)
      expect(roundWeight(0)).toBe(0)
    })
  })

  describe('roundToNearestIncrement', () => {
    it('should round to nearest increment', () => {
      expect(roundToNearestIncrement(23, 5)).toBe(25)
      expect(roundToNearestIncrement(22, 5)).toBe(20)
      expect(roundToNearestIncrement(100, 10)).toBe(100)
      expect(roundToNearestIncrement(107, 10)).toBe(110)
    })

    it('should handle increment of 0 by using 1', () => {
      expect(roundToNearestIncrement(23.7, 0)).toBe(24)
      expect(roundToNearestIncrement(22.2, 0)).toBe(22)
    })

    it('should respect minimum value', () => {
      expect(roundToNearestIncrement(5, 5, 10)).toBe(10)
      expect(roundToNearestIncrement(8, 5, 10)).toBe(10)
      expect(roundToNearestIncrement(12, 5, 10)).toBe(10)
      expect(roundToNearestIncrement(15, 5, 10)).toBe(15)
    })

    it('should respect maximum value', () => {
      expect(roundToNearestIncrement(95, 5, undefined, 90)).toBe(90)
      expect(roundToNearestIncrement(92, 5, undefined, 90)).toBe(90)
      expect(roundToNearestIncrement(88, 5, undefined, 90)).toBe(90)
      expect(roundToNearestIncrement(85, 5, undefined, 90)).toBe(85)
    })

    it('should respect both min and max values', () => {
      expect(roundToNearestIncrement(5, 5, 10, 50)).toBe(10)
      expect(roundToNearestIncrement(52, 5, 10, 50)).toBe(50)
      expect(roundToNearestIncrement(30, 5, 10, 50)).toBe(30)
    })

    it('should handle decimal increments', () => {
      expect(roundToNearestIncrement(22.3, 2.5)).toBe(22.5)
      expect(roundToNearestIncrement(22.1, 2.5)).toBe(22.5)
      expect(roundToNearestIncrement(21.1, 2.5)).toBe(20)
      expect(roundToNearestIncrement(23.8, 2.5)).toBe(25)
    })

    it('should handle small increments', () => {
      expect(roundToNearestIncrement(45.23, 0.25)).toBe(45.25)
      expect(roundToNearestIncrement(45.12, 0.25)).toBe(45)
      expect(roundToNearestIncrement(45.37, 0.25)).toBe(45.25)
      expect(roundToNearestIncrement(45.38, 0.25)).toBe(45.5)
    })
  })

  describe('truncateDecimal', () => {
    it('should truncate to specified precision', () => {
      expect(truncateDecimal(3.14159, 2)).toBe(3.14)
      expect(truncateDecimal(3.14159, 3)).toBe(3.141)
      expect(truncateDecimal(3.14159, 4)).toBe(3.1415)
    })

    it('should handle precision of 0', () => {
      expect(truncateDecimal(3.14159, 0)).toBe(3)
      expect(truncateDecimal(3.99999, 0)).toBe(3)
      expect(truncateDecimal(4.00001, 0)).toBe(4)
    })

    it('should handle negative numbers', () => {
      expect(truncateDecimal(-3.14159, 2)).toBe(-3.14)
      expect(truncateDecimal(-3.99999, 1)).toBe(-3.9)
      expect(truncateDecimal(-4.00001, 0)).toBe(-4)
    })

    it('should not round up', () => {
      expect(truncateDecimal(3.999, 2)).toBe(3.99)
      expect(truncateDecimal(3.999, 1)).toBe(3.9)
      expect(truncateDecimal(3.999, 0)).toBe(3)
    })

    it('should handle integers', () => {
      expect(truncateDecimal(42, 2)).toBe(42)
      expect(truncateDecimal(100, 0)).toBe(100)
    })

    it('should handle very small numbers', () => {
      expect(truncateDecimal(0.00123456, 4)).toBe(0.0012)
      expect(truncateDecimal(0.00123456, 5)).toBe(0.00123)
    })
  })
})
