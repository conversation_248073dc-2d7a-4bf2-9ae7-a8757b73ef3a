/**
 * Error boundary component for exercise page
 * Handles loading and workout errors with retry functionality
 */

import { useRouter } from 'next/navigation'

interface ExercisePageErrorBoundaryProps {
  error: Error
  onRetry: () => Promise<void>
}

export function ExercisePageErrorBoundary({
  error,
  onRetry,
}: ExercisePageErrorBoundaryProps) {
  const router = useRouter()

  return (
    <div className="flex flex-col items-center justify-center min-h-[50vh] p-4">
      <p className="text-red-600 mb-4">
        Failed to load exercise: {error.message}
      </p>
      <button
        className="px-4 py-2 bg-blue-600 text-white rounded-lg mb-2 min-h-[44px]"
        onClick={onRetry}
      >
        Retry
      </button>
      <button
        className="px-4 py-2 bg-gray-600 text-white rounded-lg min-h-[44px]"
        onClick={() => router.push('/workout')}
      >
        Back to Workout
      </button>
    </div>
  )
}

interface WorkoutErrorBoundaryProps {
  error: string | Error
}

export function WorkoutErrorBoundary({ error }: WorkoutErrorBoundaryProps) {
  const router = useRouter()

  return (
    <div className="flex items-center justify-center min-h-[100dvh] bg-gray-50">
      <div className="text-center p-6 max-w-md">
        <h2 className="text-2xl font-bold text-red-600 mb-4">
          Failed to Load Workout
        </h2>
        <p className="text-gray-600 mb-6">
          {typeof error === 'string'
            ? error
            : error.message || 'Unable to load workout data.'}
        </p>
        <button
          onClick={() => router.push('/workout')}
          className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors min-h-[44px]"
        >
          Back to Workout
        </button>
      </div>
    </div>
  )
}
