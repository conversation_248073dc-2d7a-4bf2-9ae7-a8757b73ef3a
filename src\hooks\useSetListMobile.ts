import { useMemo } from 'react'
import type {
  WorkoutLogSerieModelRef,
  ExerciseWorkSetsModel,
  MassUnit,
} from '@/types/api/WorkoutLogSerieModelRef'
import type { RecommendationModel, ExerciseModel } from '@/types/api'

export function useSetListMobile(
  exercise: ExerciseModel | null,
  recommendation: RecommendationModel | null,
  massUnit: MassUnit = 'kg'
) {
  const exerciseWorkSets = useMemo<ExerciseWorkSetsModel | null>(() => {
    if (!exercise || !recommendation) return null

    const sets: WorkoutLogSerieModelRef[] = []

    // Add warmup sets
    if (recommendation.WarmUpsList) {
      recommendation.WarmUpsList.forEach((warmup, i) => {
        sets.push({
          Id: -1000 - i,
          ExerciseId: exercise.Id,
          Reps: warmup.WarmUpReps,
          Weight: warmup.WarmUpWeightSet,
          IsWarmups: true,
          RIR: 0,
          IsAssisted: false,
          ExerciseName: exercise.Label,
          IsNext: false,
          IsFinished: false,
          IsActive: false,
          IsEditing: false,
          SetNo: (i + 1).toString(),
          SetTitle: `Warmup ${i + 1}`,
          IsLastSet: false,
          IsFirstSide: true,
          IsHeaderCell: false,
          IsFirstSetFinished: false,
          IsFirstWorkSet: false,
          IsExerciseFinished: false,
          IsJustSetup: false,
          ShouldUpdateIncrement: false,
          IsBackOffSet: false,
          IsNextBackOffSet: false,
          IsDropSet: false,
          IsNormalset: false,
          IsMaxChallenge: false,
          IsFlexibility: exercise.IsFlexibility || false,
          IsTimeBased: exercise.IsTimeBased || false,
          IsUnilateral: exercise.IsUnilateral || false,
          IsBodyweight: exercise.IsBodyweight || false,
          IsTimerOff: false,
          IsSizeChanged: false,
          ShowWorkTimer: false,
          LastTimeSet: '',
          PreviousReps: 0,
          PreviousWeight: { Kg: 0, Lb: 0 },
          Speed: 1.0,
          Increments: recommendation.Increments || { Kg: 2.5, Lb: 5 },
          Min: recommendation.Min || { Kg: 0, Lb: 0 },
          Max: recommendation.Max || { Kg: 200, Lb: 440 },
          HeaderImage: '',
          HeaderTitle: '',
          VideoUrl: exercise.VideoUrl || '',
          ShowPlusTooltip: false,
          ShowSuperSet3: false,
          ShowSuperSet2: false,
          get BackColor() {
            return 'transparent'
          },
          get WeightSingal() {
            if (!this.Weight) return '0.00'
            return massUnit === 'kg'
              ? (this.Weight.Kg || 0).toFixed(2)
              : (this.Weight.Lb || 0).toFixed(2)
          },
          get WeightDouble() {
            if (!this.Weight) return '0.00'
            return massUnit === 'kg'
              ? (this.Weight.Kg || 0).toFixed(2)
              : (this.Weight.Lb || 0).toFixed(2)
          },
        })
      })
    }

    // Add work sets
    const numWorkSets = parseInt(recommendation.Series.toString())
    for (let i = 0; i < numWorkSets; i++) {
      const isFirst = i === 0
      const isLast = i === numWorkSets - 1

      sets.push({
        Id: i + 1,
        ExerciseId: exercise.Id,
        Reps: recommendation.Reps,
        Weight: recommendation.Weight,
        IsWarmups: false,
        RIR: recommendation.RIR || 2,
        IsAssisted: false,
        ExerciseName: exercise.Label,
        IsNext: isFirst,
        IsFinished: false,
        IsActive: false,
        IsEditing: false,
        SetNo: (i + 1).toString(),
        SetTitle: `Set ${i + 1}`,
        IsLastSet: isLast,
        IsFirstSide: true,
        IsHeaderCell: false,
        IsFirstSetFinished: false,
        IsFirstWorkSet: isFirst,
        IsExerciseFinished: false,
        IsJustSetup: false,
        ShouldUpdateIncrement: false,
        IsBackOffSet: recommendation.IsBackOffSet || false,
        IsNextBackOffSet: false,
        IsDropSet: recommendation.IsDropSet || false,
        IsNormalset: recommendation.IsNormalSets || false,
        IsMaxChallenge: recommendation.IsMaxChallenge || false,
        IsFlexibility: exercise.IsFlexibility || false,
        IsTimeBased: exercise.IsTimeBased || false,
        IsUnilateral: exercise.IsUnilateral || false,
        IsBodyweight: exercise.IsBodyweight || false,
        IsTimerOff: false,
        IsSizeChanged: false,
        ShowWorkTimer: true,
        LastTimeSet: recommendation.LastLogDate
          ? new Date(recommendation.LastLogDate).toLocaleDateString()
          : '',
        PreviousReps: recommendation.HistorySet?.[0]?.Reps || 0,
        PreviousWeight: recommendation.HistorySet?.[0]?.Weight || {
          Kg: 0,
          Lb: 0,
        },
        Speed: recommendation.Speed || 1.0,
        Increments: recommendation.Increments || { Kg: 2.5, Lb: 5 },
        Min: recommendation.Min || { Kg: 0, Lb: 0 },
        Max: recommendation.Max || { Kg: 200, Lb: 440 },
        HeaderImage: '',
        HeaderTitle: '',
        VideoUrl: exercise.VideoUrl || '',
        ShowPlusTooltip: false,
        ShowSuperSet3: false,
        ShowSuperSet2: false,
        get BackColor() {
          if (this.IsFinished || this.IsNext) {
            return '#4D0C2432'
          }
          return 'transparent'
        },
        get WeightSingal() {
          if (!this.Weight) return '0.00'
          return massUnit === 'kg'
            ? (this.Weight.Kg || 0).toFixed(2)
            : (this.Weight.Lb || 0).toFixed(2)
        },
        get WeightDouble() {
          if (!this.Weight) return '0.00'
          return massUnit === 'kg'
            ? (this.Weight.Kg || 0).toFixed(2)
            : (this.Weight.Lb || 0).toFixed(2)
        },
      })
    }

    return {
      Id: exercise.Id,
      Label: exercise.Label,
      CountNo: '1',
      IsBodyweight: exercise.IsBodyweight || false,
      IsSystemExercise: exercise.IsSystemExercise || false,
      IsFlexibility: exercise.IsFlexibility || false,
      IsTimeBased: exercise.IsTimeBased || false,
      IsUnilateral: exercise.IsUnilateral || false,
      IsFinished: false,
      IsNextExercise: true,
      IsSelected: false,
      IsRecoLoaded: true,
      VideoUrl: exercise.VideoUrl || '',
      HeaderImage: '',
      BodyPartId: exercise.BodyPartId,
      Sets: sets,
      Clear() {
        this.Sets = []
      },
      get Count() {
        return this.Sets.length
      },
      Add(set: WorkoutLogSerieModelRef) {
        this.Sets.push(set)
      },
    }
  }, [exercise, recommendation, massUnit])

  const updateSetState = (
    setIndex: number,
    updates: Partial<WorkoutLogSerieModelRef>
  ) => {
    if (!exerciseWorkSets) return

    const set = exerciseWorkSets.Sets[setIndex]
    if (!set) return

    Object.assign(set, updates)
  }

  const markSetComplete = (setIndex: number) => {
    if (!exerciseWorkSets) return

    updateSetState(setIndex, { IsFinished: true, IsNext: false })

    // Move IsNext to next unfinished set
    const nextIndex = exerciseWorkSets.Sets.findIndex(
      (s, i) => i > setIndex && !s.IsFinished && !s.IsWarmups
    )
    if (nextIndex !== -1) {
      updateSetState(nextIndex, { IsNext: true })
    }
  }

  return {
    exerciseWorkSets,
    massUnit,
    updateSetState,
    markSetComplete,
  }
}
