import type { MultiUnityWeight } from '@/types'

/**
 * Convert weight string with comma to dot notation
 * Matches MAUI ReplaceWithDot extension method
 */
export function replaceWithDot(value: string): string {
  return value.replace(/,/g, '.')
}

/**
 * Round weight to nearest increment with min/max constraints
 * Matches MAUI RoundToNearestIncrement method
 */
export function roundToNearestIncrement(
  weight: number,
  increment: number,
  min?: number,
  max?: number
): number {
  if (increment === 0) {
    increment = 1
  }

  let rounded = Math.round(weight / increment) * increment

  if (min !== undefined && rounded < min) {
    rounded = min
  }
  if (max !== undefined && rounded > max) {
    rounded = max
  }

  return rounded
}

/**
 * Format weight for display with unit
 * Matches MAUI FormatWeight method
 */
export function formatWeight(weight: MultiUnityWeight | null, isKg: boolean): string {
  if (!weight) {
    return ''
  }

  return isKg
    ? `${Math.round(weight.Kg * 100) / 100} kg`
    : `${Math.round(weight.Lb * 100) / 100} lbs`
}

/**
 * Truncate decimal to specified precision
 * Matches MAUI TruncateDecimal method
 */
export function truncateDecimal(value: number, precision: number): number {
  const step = Math.pow(10, precision)
  const tmp = Math.trunc(step * value)
  return tmp / step
}

/**
 * Get formatted weight signal for display
 * Matches MAUI WeightSingal property logic
 */
export function getWeightSingal(weight: MultiUnityWeight | null, isKg: boolean): string {
  if (!weight) return ''

  const value = isKg ? weight.Kg : weight.Lb

  // Handle zero weights
  if (value === 0) return ''

  // Format with appropriate precision
  if (value % 1 === 0) {
    // Whole number
    return `${value}`
  } else {
    // Decimal number - show up to 2 decimal places, remove trailing zeros
    return `${parseFloat(value.toFixed(2))}`
  }
}

/**
 * Get formatted weight double for calculations
 * Matches MAUI WeightDouble property logic
 */
export function getWeightDouble(weight: MultiUnityWeight | null, isKg: boolean): string {
  if (!weight) return '0'

  const value = isKg ? weight.Kg : weight.Lb
  return `${parseFloat(value.toFixed(2))}`
}

/**
 * Get weight value for specific unit
 */
export function getWeightValue(weight: MultiUnityWeight | null, isKg: boolean): number {
  if (!weight) return 0
  return isKg ? weight.Kg : weight.Lb
}

/**
 * Format weight for input fields (always show decimals)
 */
export function formatWeightForInput(weight: MultiUnityWeight | null, isKg: boolean): string {
  if (!weight) return '0.00'

  const value = isKg ? weight.Kg : weight.Lb
  return value.toFixed(2)
}

/**
 * Parse weight input string to number
 */
export function parseWeightInput(input: string): number {
  if (!input || input.trim() === '') return 0

  // Replace comma with dot for European locales
  const normalized = replaceWithDot(input.trim())
  const parsed = parseFloat(normalized)

  return Number.isNaN(parsed) ? 0 : parsed
}

/**
 * Validate weight input
 */
export function validateWeightInput(input: string, min?: number, max?: number): {
  isValid: boolean
  value: number
  error?: string
} {
  const value = parseWeightInput(input)

  if (value < 0) {
    return { isValid: false, value, error: 'Weight cannot be negative' }
  }

  if (min !== undefined && value < min) {
    return { isValid: false, value, error: `Weight must be at least ${min}` }
  }

  if (max !== undefined && value > max) {
    return { isValid: false, value, error: `Weight cannot exceed ${max}` }
  }

  return { isValid: true, value }
}

/**
 * Create MultiUnityWeight from value and unit
 */
export function createMultiUnityWeight(value: number, unit: 'kg' | 'lb'): MultiUnityWeight {
  if (unit === 'kg') {
    return {
      Kg: value,
      Lb: value * 2.20462,
    }
  } else {
    return {
      Kg: value / 2.20462,
      Lb: value,
    }
  }
}
