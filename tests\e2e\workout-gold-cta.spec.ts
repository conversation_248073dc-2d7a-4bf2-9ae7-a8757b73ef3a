import { test, expect } from '@playwright/test'

test.describe('Workout Flow Gold CTA Buttons', () => {
  test.beforeEach(async ({ page }) => {
    // Mock the auth state
    await page.addInitScript(() => {
      window.localStorage.setItem(
        'dr-muscle-auth',
        JSON.stringify({
          state: {
            user: {
              email: '<EMAIL>',
              name: 'Test User',
              token: 'test-token',
            },
            isAuthenticated: true,
          },
          version: 0,
        })
      )
    })

    // Navigate to workout page
    await page.goto('/workout')
  })

  test('should display save set button with gold gradient styling', async ({
    page,
  }) => {
    // Click on an exercise to go to SetScreen
    const exerciseCard = page.locator('.cursor-pointer').first()
    await exerciseCard.click()

    // Wait for SetScreen to load
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Check save button styling
    const saveButton = page.getByRole('button', { name: /save set/i })
    await expect(saveButton).toBeVisible()

    // Check for gold gradient classes
    const classNames = await saveButton.getAttribute('class')
    expect(classNames).toContain('bg-gradient-metallic-gold')
    expect(classNames).toContain('shimmer-hover')
    expect(classNames).toContain('text-shadow-sm')
    expect(classNames).toContain('tracking-wider')
    expect(classNames).toContain('font-semibold')
    expect(classNames).not.toContain('uppercase')
  })

  test('should display skip button with gold gradient styling in timer screen', async ({
    page,
  }) => {
    // Navigate to rest timer page
    await page.goto('/workout/rest-timer')

    // Check skip button styling
    const skipButton = page.getByRole('button', { name: 'Skip rest timer' })
    await expect(skipButton).toBeVisible()

    // Check for gold gradient classes
    const classNames = await skipButton.getAttribute('class')
    expect(classNames).toContain('bg-gradient-metallic-gold')
    expect(classNames).toContain('shimmer-hover')
    expect(classNames).toContain('text-shadow-sm')
    expect(classNames).toContain('tracking-wider')
    expect(classNames).toContain('font-semibold')
    expect(classNames).not.toContain('uppercase')
  })

  test('should display retry button with gold gradient styling on error', async ({
    page,
  }) => {
    // Mock API failure to trigger error state
    await page.route('**/api/**', (route) => {
      route.abort('failed')
    })

    // Navigate to an exercise that will fail to load
    await page.goto('/workout/exercise/999')

    // Check retry button styling
    const retryButton = page.getByRole('button', { name: /retry/i })
    await expect(retryButton).toBeVisible()

    // Check for gold gradient classes
    const classNames = await retryButton.getAttribute('class')
    expect(classNames).toContain('bg-gradient-metallic-gold')
    expect(classNames).toContain('shimmer-hover')
    expect(classNames).toContain('text-shadow-sm')
    expect(classNames).toContain('tracking-wider')
    expect(classNames).toContain('font-semibold')
  })

  test('gold CTA buttons should have consistent hover effects', async ({
    page,
  }) => {
    // Navigate to an exercise
    const exerciseCard = page.locator('.cursor-pointer').first()
    await exerciseCard.click()
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    const saveButton = page.getByRole('button', { name: /save set/i })

    // Get initial box shadow
    const initialBoxShadow = await saveButton.evaluate(
      (el) => window.getComputedStyle(el).boxShadow
    )

    // Hover over the button
    await saveButton.hover()

    // Wait a bit for hover animation
    await page.waitForTimeout(100)

    // Get hover box shadow
    const hoverBoxShadow = await saveButton.evaluate(
      (el) => window.getComputedStyle(el).boxShadow
    )

    // Box shadow should change on hover
    expect(hoverBoxShadow).not.toBe(initialBoxShadow)
  })
})
