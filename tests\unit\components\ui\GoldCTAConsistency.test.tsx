import { describe, it, expect } from 'vitest'
import { render } from '@testing-library/react'
import { FloatingCTAButton } from '@/components/ui/FloatingCTAButton'
import { Button } from '@/components/ui/Button'

describe('Gold CTA Button Consistency', () => {
  it('should apply consistent gold gradient styling to FloatingCTAButton', () => {
    const { container } = render(<FloatingCTAButton onClick={() => {}} />)

    const button = container.querySelector('button')
    expect(button).toHaveClass('bg-gradient-metallic-gold')
    expect(button).toHaveClass('shimmer-hover')
    expect(button).toHaveClass('text-shadow-sm')
  })

  it('should apply consistent gold gradient styling to <PERSON><PERSON> with gold variant', () => {
    const { container } = render(<Button variant="gold">Gold CTA</Button>)

    const button = container.querySelector('button')
    expect(button).toHaveClass('bg-gradient-metallic-gold')
    expect(button).toHaveClass('shimmer-hover')
    expect(button).toHaveClass('text-shadow-sm')
  })

  it('should apply consistent gold gradient styling to Button with goldGradient prop', () => {
    const { container } = render(<Button goldGradient>Gold CTA</Button>)

    const button = container.querySelector('button')
    expect(button).toHaveClass('bg-gradient-metallic-gold')
    expect(button).toHaveClass('shimmer-hover')
    expect(button).toHaveClass('text-shadow-sm')
  })

  it('should have consistent shadow styling for gold CTA buttons', () => {
    const { container: floatingContainer } = render(
      <FloatingCTAButton onClick={() => {}} />
    )

    const { container: buttonContainer } = render(
      <Button variant="gold">Gold CTA</Button>
    )

    const floatingButton = floatingContainer.querySelector('button')
    const standardButton = buttonContainer.querySelector('button')

    // Both should have theme shadow classes
    expect(floatingButton).toHaveClass('shadow-theme-md')
    expect(floatingButton).toHaveClass('hover:shadow-theme-lg')
    expect(standardButton).toHaveClass('shadow-theme-md')
    expect(standardButton).toHaveClass('hover:shadow-theme-lg')
  })

  it('should maintain uppercase text styling for FloatingCTAButton', () => {
    const { container } = render(<FloatingCTAButton onClick={() => {}} />)

    const button = container.querySelector('button')
    expect(button).toHaveClass('uppercase')
    expect(button).toHaveClass('tracking-wider')
  })
})
