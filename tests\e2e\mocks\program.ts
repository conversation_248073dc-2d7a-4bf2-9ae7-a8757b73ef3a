import { Page } from '@playwright/test'

export async function mockProgramAPI(page: Page) {
  // Mock program info endpoint
  await page.route('**/api/Workout/GetUserProgramInfo*', async (route) => {
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        RecommendedProgram: {
          Id: 1,
          Label: 'Test Program',
          RemainingToLevelUp: 3,
        },
        NextWorkoutTemplate: {
          Id: 101,
          Label: 'Workout A',
          IsSystemExercise: false,
        },
      },
      TotalWorkoutCompleted: 10,
      ConsecutiveWeeks: 3,
    }

    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(mockProgramInfo),
    })
  })

  // Mock program exercises endpoint
  await page.route(
    '**/api/Workout/GetUserCustomizedCurrentWorkout',
    async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Id: 12345,
          Label: 'Day A: Chest, shoulders, and triceps',
          IsSystemExercise: true,
          Exercises: [
            {
              Id: 217,
              Label: 'Barbell bench press',
              IsBodyweight: false,
              IsEasy: false,
              IsFinished: false,
              IsSwapped: false,
              IsNextExercise: true,
              VideoUrl: 'https://example.com/video.mp4',
              LocalVideo: 'bb_bench_press.mp4',
            },
            {
              Id: 27474,
              Label: 'Dumbbell shoulder press',
              IsBodyweight: false,
              IsEasy: false,
              IsFinished: false,
              IsSwapped: false,
              IsNextExercise: false,
              VideoUrl: 'https://example.com/video2.mp4',
              LocalVideo: 'db_shoulder_press.mp4',
            },
          ],
        }),
      })
    }
  )
}