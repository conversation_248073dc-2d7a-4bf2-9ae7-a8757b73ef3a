import { renderHook, act, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import { useAuth } from '../useAuth'
import { authApi } from '@/api/auth'
import { userProfileApi } from '@/api/userProfile'
import { createQueryWrapper } from '../../../tests/test-utils'
import { debugLog } from '@/utils/debugLog'

// Mock the API modules
vi.mock('@/api/auth')
vi.mock('@/api/userProfile')
vi.mock('@/utils/userInfoPerformance', () => ({
  startSession: vi.fn(() => 'session-id'),
  endSession: vi.fn(),
}))

vi.mock('@/utils/debugLog')

describe('useAuth Error Logging', () => {
  let consoleErrorSpy: ReturnType<typeof vi.spyOn>
  let consoleWarnSpy: ReturnType<typeof vi.spyOn>

  beforeEach(() => {
    // Spy on console methods
    consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
  })

  afterEach(() => {
    consoleErrorSpy.mockRestore()
    consoleWarnSpy.mockRestore()
    vi.clearAllMocks()
  })

  describe('Login Error Logging', () => {
    it('should prefetch user info during login with smart error handling', async () => {
      // Given - successful login but 404 on user info (new user)
      const loginResponse = {
        Token: 'test-token',
        RefreshToken: 'refresh-token',
        user: { id: '123', email: '<EMAIL>' },
      }
      vi.mocked(authApi.login).mockResolvedValue(loginResponse)
      vi.mocked(userProfileApi.getUserInfo).mockRejectedValue({
        response: { status: 404 },
        message: 'Not found',
      })

      // When - login succeeds
      const { result } = renderHook(() => useAuth(), {
        wrapper: createQueryWrapper(),
      })

      await result.current.login({
        Username: '<EMAIL>',
        Password: 'password',
      })

      // Wait for prefetch to complete
      await waitFor(() => {
        expect(vi.mocked(userProfileApi.getUserInfo)).toHaveBeenCalled()
      })

      // Then - should attempt to fetch user info
      expect(vi.mocked(userProfileApi.getUserInfo)).toHaveBeenCalled()

      // But no errors should be logged for 404 (expected for new users)
      expect(vi.mocked(debugLog.warn)).not.toHaveBeenCalled()
      expect(vi.mocked(debugLog.error)).not.toHaveBeenCalled()
    })

    it('should not log console errors during smart prefetch in production', async () => {
      // Given - successful login with network error
      const loginResponse = {
        Token: 'test-token',
        RefreshToken: 'refresh-token',
        user: { id: '123', email: '<EMAIL>' },
      }
      vi.mocked(authApi.login).mockResolvedValue(loginResponse)
      vi.mocked(userProfileApi.getUserInfo).mockRejectedValue(
        new Error('network error')
      )

      // Simulate production environment
      const originalEnv = process.env.NODE_ENV
      process.env.NODE_ENV = 'production'

      try {
        // When - login succeeds
        const { result } = renderHook(() => useAuth(), {
          wrapper: createQueryWrapper(),
        })
        await result.current.login({
          Username: '<EMAIL>',
          Password: 'password',
        })

        // Wait for prefetch to complete
        await waitFor(() => {
          expect(vi.mocked(userProfileApi.getUserInfo)).toHaveBeenCalled()
        })

        // Then - prefetch calls should be made
        expect(vi.mocked(userProfileApi.getUserInfo)).toHaveBeenCalled()

        // But no console errors in production (except React act warnings)
        const nonActErrors = consoleErrorSpy.mock.calls.filter(
          (call) => !String(call[0]).includes('act(...)')
        )
        expect(nonActErrors).toHaveLength(0)
        expect(consoleWarnSpy).not.toHaveBeenCalled()
      } finally {
        process.env.NODE_ENV = originalEnv
      }
    })

    it('should handle actual login failures with proper error logging', async () => {
      // Given - login fails
      const error = new Error('Invalid credentials')
      vi.mocked(authApi.login).mockRejectedValue(error)

      // When - attempting login
      const { result } = renderHook(() => useAuth(), {
        wrapper: createQueryWrapper(),
      })

      await expect(
        result.current.login({
          Username: '<EMAIL>',
          Password: 'wrong',
        })
      ).rejects.toThrow('Invalid credentials')

      // Then - login error should be logged properly
      expect(vi.mocked(debugLog.error)).toHaveBeenCalledWith(
        'Login error:',
        expect.objectContaining({
          name: 'Error',
          message: 'Invalid credentials',
        })
      )
    })
  })

  describe('Network Error Handling', () => {
    it('should use debugLog for errors which respects NODE_ENV', async () => {
      // Given - network error
      const networkError = new Error('Network error')
      networkError.name = 'NetworkError'
      vi.mocked(authApi.login).mockRejectedValue(networkError)

      // When - attempting login
      const { result } = renderHook(() => useAuth(), {
        wrapper: createQueryWrapper(),
      })

      await act(async () => {
        await expect(
          result.current.login({
            Username: '<EMAIL>',
            Password: 'password',
          })
        ).rejects.toThrow()
      })

      // Then - in development mode, debugLog will log, but we've replaced
      // the excessive console.error calls with a single debugLog.error call
      const errorMessages = consoleErrorSpy.mock.calls.map((call) =>
        String(call[0])
      )

      // Filter out React warnings
      const appErrors = errorMessages.filter(
        (msg) => !msg.includes('act()') && !msg.includes('TestComponent')
      )

      // We should have at most one app error log (from debugLog)
      expect(appErrors.length).toBeLessThanOrEqual(1)

      // And it should not contain the old verbose logging patterns
      expect(appErrors).not.toContain('Login mutation error:')
      expect(appErrors).not.toContain('Error name:')
      expect(appErrors).not.toContain('Error message:')
      expect(appErrors).not.toContain('Full error object:')
    })
  })
})
