import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import { fileURLToPath } from 'url'

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'happy-dom',
    globals: true,
    setupFiles: './tests/setup.ts',
    css: false,

    // Exclude E2E tests from Vitest (they should only run with <PERSON><PERSON>)
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      '**/cypress/**',
      '**/.{idea,git,cache,output,temp}/**',
      '**/e2e/**',
      'tests/e2e/**',
    ],

    // Sequential execution - no parallel workers
    pool: 'forks',
    poolOptions: {
      forks: {
        singleFork: true, // Force single process
      }
    },
    
    // Disable isolation for better performance
    isolate: false,
    
    // Minimal reporter for better performance
    reporters: process.env.CI ? ['verbose'] : ['dot'],
    
    // Disable coverage during regular test runs for speed
    coverage: {
      enabled: false,
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'text-summary'],
      exclude: [
        'node_modules/',
        'tests/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/mockData/**',
        'src/types/**',
      ],
      thresholds: {
        lines: 80,
        functions: 80,
        branches: 80,
        statements: 80,
      },
    },
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
})